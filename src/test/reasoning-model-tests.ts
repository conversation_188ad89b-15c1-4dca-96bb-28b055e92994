import { z } from 'zod';
import { 
  createReasoningAwareAdapter,
  SmartAdapterFactory,
  UnifiedModelAdapter,
  ReasoningErrorHandler,
  RecoveryStrategy
} from '../core/ai';
import { Message } from '../core/ai/interfaces';

/**
 * 推理模型测试工具
 * 用于验证推理模型处理功能的正确性
 */

// 测试用的表单数据 Schema
const FormDataSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  address: z.string(),
  message: z.string().optional()
});

type FormData = z.infer<typeof FormDataSchema>;

// 模拟的推理模型响应数据
const MOCK_REASONING_RESPONSES = {
  // DeepSeek 推理模型响应
  deepseek: {
    withThinking: `<think>
用户需要填充一个表单。让我分析一下需要什么信息：
1. name: 需要一个合理的姓名
2. email: 需要一个有效的邮箱地址
3. phone: 需要一个电话号码
4. address: 需要一个地址
5. message: 可选的消息内容

我需要生成一个JSON对象包含这些字段。
</think>

{
  "name": "张三",
  "email": "<EMAIL>", 
  "phone": "13800138000",
  "address": "北京市朝阳区某某街道123号",
  "message": "这是一条测试消息"
}`,
    
    malformed: `<think>
Let me think about this form data...
The user wants me to fill out a form with personal information.
</think>

{
  "name": "Test User"
  "email": "<EMAIL>",  // 这里有语法错误
  "phone": "************"
  "address": "123 Test Street"
}`,

    withoutTags: `{
  "name": "直接返回用户",
  "email": "<EMAIL>",
  "phone": "13900139000", 
  "address": "上海市浦东新区测试路456号",
  "message": "没有推理标签的响应"
}`
  },
  
  // OpenAI O1 推理模型响应
  openai: {
    withThinking: `<thinking>
I need to generate form data for a user. Let me consider what would be appropriate:
- name: Should be a realistic name
- email: Must be a valid email format
- phone: Should follow a standard phone format
- address: Should be a complete address
- message: Optional but can add value

Let me create a JSON response with this information.
</thinking>

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "555-0123",
  "address": "123 Main Street, Anytown, NY 12345",
  "message": "Thank you for your service"
}`
  }
};

/**
 * 测试用例接口
 */
interface TestCase {
  name: string;
  description: string;
  modelId: string;
  provider: string;
  input: {
    messages: Message[];
    options?: any;
  };
  mockResponse: string;
  expectedOutput: {
    shouldExtractReasoning: boolean;
    shouldParseJSON: boolean;
    shouldValidateSchema: boolean;
    reasoningLength?: number;
    hasValidFormData?: boolean;
  };
}

/**
 * 推理模型处理测试套件
 */
export class ReasoningModelTestSuite {
  private testResults: Array<{
    testCase: TestCase;
    result: 'passed' | 'failed';
    details: any;
    error?: Error;
    timing: {
      startTime: number;
      endTime: number;
      duration: number;
    };
  }> = [];

  /**
   * 创建测试用例
   */
  private createTestCases(): TestCase[] {
    return [
      // DeepSeek 推理模型测试
      {
        name: 'DeepSeek Reasoning - Normal Case',
        description: '测试 DeepSeek 推理模型正常处理流程',
        modelId: 'deepseek-reasoner',
        provider: 'deepseek',
        input: {
          messages: [{
            role: 'user',
            content: '请帮我填充一个表单，包含姓名、邮箱、电话、地址等信息',
            timestamp: Date.now()
          }]
        },
        mockResponse: MOCK_REASONING_RESPONSES.deepseek.withThinking,
        expectedOutput: {
          shouldExtractReasoning: true,
          shouldParseJSON: true,
          shouldValidateSchema: true,
          reasoningLength: 50, // 预期推理内容长度
          hasValidFormData: true
        }
      },

      // 格式错误测试
      {
        name: 'DeepSeek Reasoning - Malformed JSON',
        description: '测试推理模型返回格式错误JSON时的处理',
        modelId: 'deepseek-reasoner',
        provider: 'deepseek',
        input: {
          messages: [{
            role: 'user',
            content: 'Fill out a form with user information',
            timestamp: Date.now()
          }]
        },
        mockResponse: MOCK_REASONING_RESPONSES.deepseek.malformed,
        expectedOutput: {
          shouldExtractReasoning: true,
          shouldParseJSON: false, // 应该解析失败
          shouldValidateSchema: false,
          reasoningLength: 30
        }
      },

      // 无推理标签测试
      {
        name: 'DeepSeek Reasoning - No Thinking Tags',
        description: '测试推理模型返回无思考标签时的处理',
        modelId: 'deepseek-reasoner',
        provider: 'deepseek',
        input: {
          messages: [{
            role: 'user',
            content: '生成表单数据',
            timestamp: Date.now()
          }]
        },
        mockResponse: MOCK_REASONING_RESPONSES.deepseek.withoutTags,
        expectedOutput: {
          shouldExtractReasoning: false, // 没有推理内容
          shouldParseJSON: true,
          shouldValidateSchema: true,
          hasValidFormData: true
        }
      },

      // OpenAI O1 测试
      {
        name: 'OpenAI O1 Reasoning - Normal Case',
        description: '测试 OpenAI O1 推理模型处理',
        modelId: 'o1-preview',
        provider: 'openai',
        input: {
          messages: [{
            role: 'user',
            content: 'Generate form data for a contact form',
            timestamp: Date.now()
          }]
        },
        mockResponse: MOCK_REASONING_RESPONSES.openai.withThinking,
        expectedOutput: {
          shouldExtractReasoning: true,
          shouldParseJSON: true,
          shouldValidateSchema: true,
          reasoningLength: 40,
          hasValidFormData: true
        }
      },

      // 非推理模型测试（对照组）
      {
        name: 'Standard Model - Control Test',
        description: '测试普通模型处理（对照组）',
        modelId: 'gpt-4',
        provider: 'openai',
        input: {
          messages: [{
            role: 'user',
            content: 'Generate form data',
            timestamp: Date.now()
          }]
        },
        mockResponse: MOCK_REASONING_RESPONSES.deepseek.withoutTags,
        expectedOutput: {
          shouldExtractReasoning: false, // 普通模型不应该提取推理
          shouldParseJSON: true,
          shouldValidateSchema: true,
          hasValidFormData: true
        }
      }
    ];
  }

  /**
   * 运行单个测试用例
   */
  private async runSingleTest(testCase: TestCase): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 Running test: ${testCase.name}`);
      
      // 创建适配器
      const adapter = await SmartAdapterFactory.createAdapter(
        testCase.modelId,
        'test-api-key',
        'test-endpoint'
      );

      // 模拟 AI 响应（替换实际网络调用）
      this.mockAdapterResponse(adapter, testCase.mockResponse);

      // 执行结构化输出生成
      const result = await adapter.generateStructured(
        testCase.input.messages,
        FormDataSchema,
        testCase.input.options
      );

      // 验证结果
      const validationResult = this.validateTestResult(result, testCase.expectedOutput);
      
      const endTime = Date.now();
      
      this.testResults.push({
        testCase,
        result: validationResult.success ? 'passed' : 'failed',
        details: {
          actualOutput: result,
          validation: validationResult,
          mockResponse: testCase.mockResponse
        },
        timing: {
          startTime,
          endTime,
          duration: endTime - startTime
        }
      });

      console.log(`✅ Test passed: ${testCase.name}`);
      
    } catch (error) {
      const endTime = Date.now();
      
      this.testResults.push({
        testCase,
        result: 'failed',
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        error: error instanceof Error ? error : new Error(String(error)),
        timing: {
          startTime,
          endTime,
          duration: endTime - startTime
        }
      });

      console.log(`❌ Test failed: ${testCase.name} - ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 模拟适配器响应（用于测试）
   */
  private mockAdapterResponse(adapter: UnifiedModelAdapter, mockResponse: string): void {
    // 这里我们需要重写适配器的内部生成方法
    // 在实际实现中，这可能需要通过依赖注入或测试框架来实现
    (adapter as any).generateTextInternal = async () => {
      return {
        content: mockResponse,
        usage: { promptTokens: 100, completionTokens: 200, totalTokens: 300 },
        metadata: { mocked: true }
      };
    };

    (adapter as any).generateStructuredInternal = async (messages: any, schema: any) => {
      // 模拟推理处理逻辑
      const adapterInfo = adapter.getInfo();
      if (adapterInfo.isReasoningModel && adapterInfo.reasoningEnabled) {
        // 模拟推理处理
        const { reasoning, text } = this.extractReasoningFromMock(mockResponse);
        
        try {
          const jsonData = JSON.parse(text);
          const validatedData = schema.parse(jsonData);
          
          return {
            data: validatedData,
            content: text,
            reasoning: reasoning,
            usage: { promptTokens: 100, completionTokens: 200, totalTokens: 300 },
            metadata: { 
              mocked: true,
              hasReasoning: !!reasoning,
              strategy: 'mocked-reasoning-aware'
            }
          };
        } catch (error) {
          throw new Error(`Mock JSON parsing failed: ${error}`);
        }
      } else {
        // 普通模型处理
        try {
          const jsonData = JSON.parse(mockResponse);
          const validatedData = schema.parse(jsonData);
          
          return {
            data: validatedData,
            content: mockResponse,
            usage: { promptTokens: 100, completionTokens: 200, totalTokens: 300 },
            metadata: { 
              mocked: true,
              strategy: 'mocked-standard'
            }
          };
        } catch (error) {
          throw new Error(`Mock JSON parsing failed: ${error}`);
        }
      }
    };
  }

  /**
   * 从模拟响应中提取推理内容
   */
  private extractReasoningFromMock(response: string): { reasoning?: string; text: string } {
    // 提取 <think> 标签
    const thinkMatch = response.match(/<think>(.*?)<\/think>/s);
    if (thinkMatch) {
      const reasoning = thinkMatch[1].trim();
      const text = response.replace(/<think>.*?<\/think>/s, '').trim();
      return { reasoning, text };
    }

    // 提取 <thinking> 标签
    const thinkingMatch = response.match(/<thinking>(.*?)<\/thinking>/s);
    if (thinkingMatch) {
      const reasoning = thinkingMatch[1].trim();
      const text = response.replace(/<thinking>.*?<\/thinking>/s, '').trim();
      return { reasoning, text };
    }

    // 没有推理标签
    return { text: response };
  }

  /**
   * 验证测试结果
   */
  private validateTestResult(actualResult: any, expectedOutput: TestCase['expectedOutput']): {
    success: boolean;
    details: Record<string, any>;
  } {
    const details: Record<string, any> = {};
    let success = true;

    // 检查是否应该提取推理
    if (expectedOutput.shouldExtractReasoning) {
      const hasReasoning = !!actualResult.reasoning;
      details.reasoningExtracted = hasReasoning;
      if (!hasReasoning) {
        success = false;
        details.reasoningError = 'Expected reasoning extraction but none found';
      } else if (expectedOutput.reasoningLength) {
        const actualLength = actualResult.reasoning.length;
        details.reasoningLength = actualLength;
        if (actualLength < expectedOutput.reasoningLength) {
          details.reasoningLengthWarning = `Reasoning shorter than expected (${actualLength} < ${expectedOutput.reasoningLength})`;
        }
      }
    } else {
      if (actualResult.reasoning) {
        details.unexpectedReasoning = 'Found reasoning when none was expected';
        success = false;
      }
    }

    // 检查JSON解析
    if (expectedOutput.shouldParseJSON) {
      const hasValidData = !!actualResult.data;
      details.jsonParsed = hasValidData;
      if (!hasValidData) {
        success = false;
        details.jsonError = 'Expected valid JSON data but parsing failed';
      }
    }

    // 检查Schema验证
    if (expectedOutput.shouldValidateSchema && expectedOutput.hasValidFormData) {
      if (actualResult.data) {
        try {
          FormDataSchema.parse(actualResult.data);
          details.schemaValid = true;
        } catch (error) {
          details.schemaValid = false;
          details.schemaError = error instanceof Error ? error.message : String(error);
          success = false;
        }
      }
    }

    return { success, details };
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Reasoning Model Test Suite');
    console.log('=====================================\n');

    const testCases = this.createTestCases();
    
    for (const testCase of testCases) {
      await this.runSingleTest(testCase);
      // 测试之间稍微延迟
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.generateTestReport();
  }

  /**
   * 生成测试报告
   */
  private generateTestReport(): void {
    console.log('\n📊 Test Results Summary');
    console.log('========================');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'passed').length;
    const failedTests = totalTests - passedTests;
    const successRate = (passedTests / totalTests * 100).toFixed(1);

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${successRate}%`);

    // 详细结果
    console.log('\n📋 Detailed Results:');
    console.log('--------------------');
    
    this.testResults.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.testCase.name}`);
      console.log(`   Status: ${result.result === 'passed' ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`   Duration: ${result.timing.duration}ms`);
      
      if (result.result === 'failed') {
        if (result.error) {
          console.log(`   Error: ${result.error.message}`);
        }
        if (result.details.validation) {
          console.log(`   Validation Details:`, result.details.validation.details);
        }
      } else {
        if (result.details.validation) {
          console.log(`   Validation: All checks passed`);
        }
      }
    });

    // 性能统计
    const avgDuration = this.testResults.reduce((sum, r) => sum + r.timing.duration, 0) / totalTests;
    const maxDuration = Math.max(...this.testResults.map(r => r.timing.duration));
    const minDuration = Math.min(...this.testResults.map(r => r.timing.duration));

    console.log('\n⏱️  Performance Statistics:');
    console.log('---------------------------');
    console.log(`Average Duration: ${avgDuration.toFixed(1)}ms`);
    console.log(`Max Duration: ${maxDuration}ms`);
    console.log(`Min Duration: ${minDuration}ms`);
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return {
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.result === 'passed').length,
        failed: this.testResults.filter(r => r.result === 'failed').length,
        successRate: (this.testResults.filter(r => r.result === 'passed').length / this.testResults.length * 100)
      },
      details: this.testResults
    };
  }
}

/**
 * 错误恢复机制测试
 */
export class ErrorRecoveryTestSuite {
  
  /**
   * 测试错误恢复策略
   */
  async testErrorRecoveryStrategies(): Promise<void> {
    console.log('\n🔧 Testing Error Recovery Mechanisms');
    console.log('====================================');

    const errorHandler = new ReasoningErrorHandler({
      maxRetries: 2,
      retryInterval: 100, // 快速测试
      enabledStrategies: [
        RecoveryStrategy.DISABLE_REASONING,
        RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT,
        RecoveryStrategy.IMMEDIATE_RETRY
      ]
    });

    // 测试推理功能降级
    await this.testReasoningDisableRecovery(errorHandler);
    
    // 测试结构化输出降级
    await this.testStructuredOutputDisableRecovery(errorHandler);
    
    // 测试立即重试
    await this.testImmediateRetryRecovery(errorHandler);

    console.log('✅ Error recovery mechanism tests completed');
  }

  private async testReasoningDisableRecovery(handler: ReasoningErrorHandler): Promise<void> {
    console.log('\n🔄 Testing reasoning disable recovery...');
    
    // 模拟推理相关错误
    const mockError = new Error('Failed to extract thinking content from response');
    const errorContext = {
      modelId: 'deepseek-reasoner',
      provider: 'deepseek',
      isReasoningModel: true,
      attemptNumber: 1,
      originalError: mockError,
      enabledFeatures: {
        reasoning: true,
        structuredOutput: true
      },
      timestamp: Date.now()
    };

    const result = await handler.handleError(mockError, errorContext);
    
    if (result.success && result.strategy === RecoveryStrategy.DISABLE_REASONING) {
      console.log('✅ Reasoning disable recovery strategy working correctly');
    } else {
      console.log('❌ Reasoning disable recovery strategy failed');
    }
  }

  private async testStructuredOutputDisableRecovery(handler: ReasoningErrorHandler): Promise<void> {
    console.log('\n📊 Testing structured output disable recovery...');
    
    // 模拟结构化输出相关错误
    const mockError = new Error('JSON parsing failed - invalid format');
    const errorContext = {
      modelId: 'gpt-4',
      provider: 'openai',
      isReasoningModel: false,
      attemptNumber: 1,
      originalError: mockError,
      enabledFeatures: {
        reasoning: false,
        structuredOutput: true
      },
      timestamp: Date.now()
    };

    const result = await handler.handleError(mockError, errorContext);
    
    if (result.success && result.strategy === RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT) {
      console.log('✅ Structured output disable recovery strategy working correctly');
    } else {
      console.log('❌ Structured output disable recovery strategy failed');
    }
  }

  private async testImmediateRetryRecovery(handler: ReasoningErrorHandler): Promise<void> {
    console.log('\n⚡ Testing immediate retry recovery...');
    
    // 模拟网络错误
    const mockError = new Error('Network timeout');
    const errorContext = {
      modelId: 'claude-3-sonnet',
      provider: 'claude',
      isReasoningModel: false,
      attemptNumber: 1,
      originalError: mockError,
      enabledFeatures: {
        reasoning: false,
        structuredOutput: true
      },
      timestamp: Date.now()
    };

    const result = await handler.handleError(mockError, errorContext);
    
    if (result.success && result.strategy === RecoveryStrategy.IMMEDIATE_RETRY) {
      console.log('✅ Immediate retry recovery strategy working correctly');
    } else {
      console.log('❌ Immediate retry recovery strategy failed');
    }
  }
}

/**
 * 运行完整的测试套件
 */
export async function runCompleteTestSuite(): Promise<void> {
  console.log('🎯 Fillify Reasoning Model Test Suite');
  console.log('=====================================');
  console.log('Testing the complete reasoning model processing pipeline...\n');

  try {
    // 运行主要功能测试
    const reasoningTests = new ReasoningModelTestSuite();
    await reasoningTests.runAllTests();

    // 运行错误恢复测试
    const errorTests = new ErrorRecoveryTestSuite();
    await errorTests.testErrorRecoveryStrategies();

    // 获取最终结果
    const results = reasoningTests.getTestResults();
    
    console.log('\n🎉 Complete Test Suite Finished!');
    console.log('================================');
    console.log(`Final Success Rate: ${results.summary.successRate.toFixed(1)}%`);
    
    if (results.summary.successRate >= 80) {
      console.log('🎯 推理模型处理系统已准备投入生产环境！');
    } else {
      console.log('⚠️  建议在投入生产前解决测试失败的问题');
    }

  } catch (error) {
    console.error('❌ Test suite execution failed:', error);
  }
}

// 导出便捷函数
export const testReasoningModelProcessing = runCompleteTestSuite;