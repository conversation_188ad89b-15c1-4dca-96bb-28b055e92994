#!/usr/bin/env node

/**
 * 推理模型处理系统 - 快速验证脚本
 * 
 * 这个脚本可以快速验证推理模型处理系统的各个组件是否正常工作
 * 无需真实的API调用，使用模拟数据进行功能验证
 */

import { runCompleteTestSuite } from '../test/reasoning-model-tests';
import { runReasoningModelDemo } from '../demo/reasoning-model-demo';

/**
 * 快速健康检查
 */
async function quickHealthCheck() {
  console.log('🔍 快速健康检查');
  console.log('================\n');

  const checks = [
    {
      name: '推理中间件模块',
      check: async () => {
        const { createReasoningWrapper, ReasoningContentParser } = await import('../core/ai/reasoning-middleware');
        const wrapper = createReasoningWrapper('deepseek-reasoner');
        const parser = new ReasoningContentParser({ enabled: true, tagName: 'think' });
        return wrapper && parser;
      }
    },
    {
      name: '结构化输出管理器',
      check: async () => {
        const { structuredOutputManager } = await import('../core/ai/structured-output');
        return !!structuredOutputManager;
      }
    },
    {
      name: '统一适配器系统',
      check: async () => {
        const { createUnifiedModelAdapter } = await import('../core/ai/unified-adapter');
        const adapter = createUnifiedModelAdapter({
          modelId: 'test-model',
          provider: 'test-provider',
          enableReasoning: true
        });
        return !!adapter;
      }
    },
    {
      name: '错误处理机制',
      check: async () => {
        const { ReasoningErrorHandler, globalReasoningErrorHandler } = await import('../core/ai/reasoning-error-handler');
        return globalReasoningErrorHandler instanceof ReasoningErrorHandler;
      }
    },
    {
      name: '智能适配器工厂',
      check: async () => {
        const { SmartAdapterFactory } = await import('../core/ai/smart-adapter-factory');
        const supportedModels = SmartAdapterFactory.getSupportedReasoningModels();
        return supportedModels.length > 0;
      }
    }
  ];

  let allPassed = true;

  for (const { name, check } of checks) {
    try {
      const result = await check();
      if (result) {
        console.log(`✅ ${name}`);
      } else {
        console.log(`❌ ${name} - 检查失败`);
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${name} - 异常: ${error instanceof Error ? error.message : error}`);
      allPassed = false;
    }
  }

  console.log(`\n${allPassed ? '✅ 所有组件检查通过' : '❌ 部分组件检查失败'}\n`);
  return allPassed;
}

/**
 * 配置验证
 */
async function validateConfiguration() {
  console.log('⚙️  配置验证');
  console.log('============\n');

  try {
    const { SmartAdapterFactory } = await import('../core/ai/smart-adapter-factory');
    
    // 验证支持的推理模型
    const reasoningModels = SmartAdapterFactory.getSupportedReasoningModels();
    console.log('🧠 支持的推理模型:');
    reasoningModels.forEach(model => {
      console.log(`   • ${model}`);
    });

    // 验证支持的标准模型
    const standardModels = SmartAdapterFactory.getSupportedStandardModels();
    console.log('\n🤖 支持的标准模型:');
    standardModels.forEach(model => {
      console.log(`   • ${model}`);
    });

    // 验证缓存状态
    const cacheStats = SmartAdapterFactory.getCacheStats();
    console.log('\n💾 适配器缓存状态:');
    console.log(`   缓存大小: ${cacheStats.size}`);
    console.log(`   已缓存模型: ${cacheStats.models.length}`);

    console.log('\n✅ 配置验证完成\n');
    return true;

  } catch (error) {
    console.log(`❌ 配置验证失败: ${error instanceof Error ? error.message : error}\n`);
    return false;
  }
}

/**
 * 基本功能验证
 */
async function validateBasicFunctionality() {
  console.log('🧪 基本功能验证');
  console.log('================\n');

  try {
    // 验证推理内容解析
    const { ReasoningContentParser } = await import('../core/ai/reasoning-middleware');
    const parser = new ReasoningContentParser({
      enabled: true,
      tagName: 'think',
      separator: '\n\n'
    });

    const testContent = '<think>这是推理过程</think>这是最终答案';
    const parsed = parser.parseReasoningContent(testContent);
    
    if (parsed.reasoning === '这是推理过程' && parsed.text === '这是最终答案') {
      console.log('✅ 推理内容解析功能正常');
    } else {
      console.log('❌ 推理内容解析功能异常');
      return false;
    }

    // 验证模型识别
    const { createReasoningWrapper } = await import('../core/ai/reasoning-middleware');
    const wrapper = createReasoningWrapper('deepseek-reasoner');
    
    if (wrapper.isReasoningModel('deepseek-reasoner') && !wrapper.isReasoningModel('gpt-4')) {
      console.log('✅ 推理模型识别功能正常');
    } else {
      console.log('❌ 推理模型识别功能异常');
      return false;
    }

    // 验证错误分类
    const { globalReasoningErrorHandler } = await import('../core/ai/reasoning-error-handler');
    const errorStats = globalReasoningErrorHandler.getErrorStats();
    
    if (typeof errorStats.totalErrors === 'number') {
      console.log('✅ 错误处理统计功能正常');
    } else {
      console.log('❌ 错误处理统计功能异常');
      return false;
    }

    console.log('\n✅ 基本功能验证完成\n');
    return true;

  } catch (error) {
    console.log(`❌ 基本功能验证失败: ${error instanceof Error ? error.message : error}\n`);
    return false;
  }
}

/**
 * 运行选项
 */
async function showMenu() {
  console.log('🎯 Fillify 推理模型处理系统 - 验证工具');
  console.log('==========================================\n');
  
  console.log('请选择要执行的操作:');
  console.log('1. 快速健康检查');
  console.log('2. 配置验证');
  console.log('3. 基本功能验证');
  console.log('4. 运行完整测试套件');
  console.log('5. 运行演示');
  console.log('6. 运行所有验证');
  console.log('0. 退出\n');
}

/**
 * 处理用户选择
 */
async function handleChoice(choice: string) {
  switch (choice.trim()) {
    case '1':
      return await quickHealthCheck();
    
    case '2':
      return await validateConfiguration();
    
    case '3':
      return await validateBasicFunctionality();
    
    case '4':
      await runCompleteTestSuite();
      return true;
    
    case '5':
      await runReasoningModelDemo();
      return true;
    
    case '6':
      console.log('🚀 运行完整验证流程\n');
      const healthOk = await quickHealthCheck();
      const configOk = await validateConfiguration();
      const functionalOk = await validateBasicFunctionality();
      
      if (healthOk && configOk && functionalOk) {
        console.log('🎉 所有基本验证通过，开始运行完整测试...\n');
        await runCompleteTestSuite();
      } else {
        console.log('⚠️  基本验证未完全通过，建议检查问题后再运行完整测试\n');
      }
      return true;
    
    case '0':
      console.log('👋 再见！');
      return false;
    
    default:
      console.log('❌ 无效选择，请重新选择\n');
      return true;
  }
}

/**
 * 主函数
 */
async function main() {
  // 检查是否在命令行模式
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 命令行模式
    const command = args[0];
    switch (command) {
      case 'health':
        await quickHealthCheck();
        break;
      case 'config':
        await validateConfiguration();
        break;
      case 'function':
        await validateBasicFunctionality();
        break;
      case 'test':
        await runCompleteTestSuite();
        break;
      case 'demo':
        await runReasoningModelDemo();
        break;
      case 'all':
        await handleChoice('6');
        break;
      default:
        console.log('用法: node verify.js [health|config|function|test|demo|all]');
        process.exit(1);
    }
    return;
  }

  // 交互模式
  try {
    // 注意：在实际的Node.js环境中，你需要使用readline模块来处理用户输入
    // 这里我们简化处理，直接运行完整验证
    console.log('🚀 自动运行完整验证流程（非交互模式）\n');
    await handleChoice('6');
    
  } catch (error) {
    console.error('❌ 执行过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果这个文件被直接运行，则执行主函数
if (require.main === module) {
  main().catch(console.error);
}

// 导出验证函数供其他模块使用
export {
  quickHealthCheck,
  validateConfiguration,
  validateBasicFunctionality
};