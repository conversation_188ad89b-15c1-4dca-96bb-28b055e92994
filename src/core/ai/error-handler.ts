import { AIError, AIErrorType, AIModelError } from './interfaces';

// Error reporter interface for collecting and reporting errors
export interface ErrorReporter {
  report(error: AIModelError, context?: Record<string, any>): void;
  getErrorStats(): ErrorStats;
}

export interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<AIErrorType, number>;
  recentErrors: Array<{
    type: AIErrorType;
    message: string;
    timestamp: number;
    context?: Record<string, any>;
  }>;
}

// Console-based error reporter (default)
export class ConsoleErrorReporter implements ErrorReporter {
  private stats: ErrorStats = {
    totalErrors: 0,
    errorsByType: {} as Record<AIErrorType, number>,
    recentErrors: [],
  };

  report(error: AIModelError, context?: Record<string, any>): void {
    // Update statistics
    this.stats.totalErrors++;
    this.stats.errorsByType[error.type] = (this.stats.errorsByType[error.type] || 0) + 1;
    
    // Keep only last 100 errors
    this.stats.recentErrors.push({
      type: error.type,
      message: error.message,
      timestamp: Date.now(),
      context,
    });
    
    if (this.stats.recentErrors.length > 100) {
      this.stats.recentErrors.shift();
    }

    // Log to console with appropriate level
    const logLevel = this.getLogLevel(error.type);
    const logMessage = `[AI Error] ${error.type}: ${error.message}`;
    const logData = {
      error,
      context,
      retryable: error.retryable,
      fallbackOptions: error.fallbackOptions,
    };

    switch (logLevel) {
      case 'error':
        console.error(logMessage, logData);
        break;
      case 'warn':
        console.warn(logMessage, logData);
        break;
      case 'info':
        console.info(logMessage, logData);
        break;
      default:
        console.log(logMessage, logData);
    }
  }

  getErrorStats(): ErrorStats {
    return { ...this.stats };
  }

  private getLogLevel(errorType: AIErrorType): 'error' | 'warn' | 'info' | 'log' {
    switch (errorType) {
      case 'CONFIGURATION_ERROR':
      case 'AUTHENTICATION_ERROR':
        return 'error';
      
      case 'RATE_LIMIT_ERROR':
      case 'CONTEXT_LENGTH_ERROR':
      case 'MODEL_UNAVAILABLE_ERROR':
        return 'warn';
      
      case 'PARSING_ERROR':
      case 'VALIDATION_ERROR':
        return 'info';
      
      default:
        return 'log';
    }
  }
}

// Error recovery strategies
export interface RecoveryStrategy {
  name: string;
  canRecover(error: AIModelError): boolean;
  recover(error: AIModelError, context: any): Promise<any>;
}

export class ApiKeyRecoveryStrategy implements RecoveryStrategy {
  name = 'api-key-recovery';

  canRecover(error: AIModelError): boolean {
    return error.type === 'AUTHENTICATION_ERROR';
  }

  async recover(error: AIModelError, context: any): Promise<any> {
    // Prompt user for new API key or redirect to settings
    throw new AIModelError({
      type: 'CONFIGURATION_ERROR',
      message: 'Please check your API key in settings',
      retryable: false,
    });
  }
}

export class RateLimitRecoveryStrategy implements RecoveryStrategy {
  name = 'rate-limit-recovery';

  canRecover(error: AIModelError): boolean {
    return error.type === 'RATE_LIMIT_ERROR';
  }

  async recover(error: AIModelError, context: any): Promise<any> {
    // Extract wait time from error message if available
    const waitTimeMatch = error.message.match(/(\d+)\s*seconds?/i);
    const waitTime = waitTimeMatch ? parseInt(waitTimeMatch[1]) * 1000 : 5000;
    
    console.info(`Rate limit hit, waiting ${waitTime}ms before retry`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
    
    return context; // Return original context to retry
  }
}

export class ContextLengthRecoveryStrategy implements RecoveryStrategy {
  name = 'context-length-recovery';

  canRecover(error: AIModelError): boolean {
    return error.type === 'CONTEXT_LENGTH_ERROR';
  }

  async recover(error: AIModelError, context: any): Promise<any> {
    // Reduce context size by removing older messages
    if (context.messages && Array.isArray(context.messages)) {
      const messages = context.messages;
      if (messages.length > 2) {
        // Keep system message (if present) and last user message
        const systemMessages = messages.filter(m => m.role === 'system');
        const lastUserMessage = messages.filter(m => m.role === 'user').slice(-1);
        
        return {
          ...context,
          messages: [...systemMessages, ...lastUserMessage],
        };
      }
    }

    // If we can't reduce further, fail
    throw new AIModelError({
      type: 'CONTEXT_LENGTH_ERROR',
      message: 'Cannot reduce context length further',
      retryable: false,
    });
  }
}

// Error handler with recovery capabilities
export class AIErrorHandler {
  private reporter: ErrorReporter;
  private recoveryStrategies: RecoveryStrategy[] = [];

  constructor(reporter: ErrorReporter = new ConsoleErrorReporter()) {
    this.reporter = reporter;
    this.setupDefaultRecoveryStrategies();
  }

  addRecoveryStrategy(strategy: RecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
  }

  removeRecoveryStrategy(name: string): void {
    this.recoveryStrategies = this.recoveryStrategies.filter(s => s.name !== name);
  }

  async handleError(
    error: unknown, 
    context?: Record<string, any>
  ): Promise<AIModelError> {
    // Convert to AIModelError if necessary
    let aiError: AIModelError;
    if (error instanceof AIModelError) {
      aiError = error;
    } else {
      aiError = AIModelError.fromUnknown(error);
    }

    // Report the error
    this.reporter.report(aiError, context);

    // Try recovery strategies
    for (const strategy of this.recoveryStrategies) {
      if (strategy.canRecover(aiError)) {
        try {
          console.info(`Attempting recovery with strategy: ${strategy.name}`);
          const recoveredContext = await strategy.recover(aiError, context);
          
          // Mark error as recovered for potential retry
          return new AIModelError({
            ...aiError,
            message: `${aiError.message} (recovered via ${strategy.name})`,
            retryable: true,
          });
        } catch (recoveryError) {
          console.warn(`Recovery strategy ${strategy.name} failed:`, recoveryError);
        }
      }
    }

    return aiError;
  }

  getErrorStats(): ErrorStats {
    return this.reporter.getErrorStats();
  }

  private setupDefaultRecoveryStrategies(): void {
    this.addRecoveryStrategy(new ApiKeyRecoveryStrategy());
    this.addRecoveryStrategy(new RateLimitRecoveryStrategy());
    this.addRecoveryStrategy(new ContextLengthRecoveryStrategy());
  }
}

// Error classification utilities
export class ErrorClassifier {
  static isRetryable(error: AIModelError): boolean {
    return error.retryable;
  }

  static requiresUserAction(error: AIModelError): boolean {
    return ['CONFIGURATION_ERROR', 'AUTHENTICATION_ERROR'].includes(error.type);
  }

  static isTemporary(error: AIModelError): boolean {
    return [
      'RATE_LIMIT_ERROR',
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'MODEL_UNAVAILABLE_ERROR',
    ].includes(error.type);
  }

  static isRecoverable(error: AIModelError): boolean {
    return [
      'RATE_LIMIT_ERROR',
      'CONTEXT_LENGTH_ERROR',
      'PARSING_ERROR',
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
    ].includes(error.type);
  }

  static getSeverity(error: AIModelError): 'low' | 'medium' | 'high' | 'critical' {
    switch (error.type) {
      case 'CONFIGURATION_ERROR':
      case 'AUTHENTICATION_ERROR':
        return 'critical';
      
      case 'MODEL_UNAVAILABLE_ERROR':
      case 'CONTEXT_LENGTH_ERROR':
        return 'high';
      
      case 'RATE_LIMIT_ERROR':
      case 'NETWORK_ERROR':
      case 'TIMEOUT_ERROR':
        return 'medium';
      
      case 'PARSING_ERROR':
      case 'VALIDATION_ERROR':
        return 'low';
      
      default:
        return 'medium';
    }
  }

  static getRecommendedAction(error: AIModelError): string {
    switch (error.type) {
      case 'CONFIGURATION_ERROR':
        return 'Check your configuration settings';
      
      case 'AUTHENTICATION_ERROR':
        return 'Verify your API key is correct and has sufficient permissions';
      
      case 'RATE_LIMIT_ERROR':
        return 'Wait before retrying or upgrade your API plan';
      
      case 'CONTEXT_LENGTH_ERROR':
        return 'Reduce the length of your input or use a model with larger context';
      
      case 'MODEL_UNAVAILABLE_ERROR':
        return 'Try a different model or check service status';
      
      case 'NETWORK_ERROR':
        return 'Check your internet connection and try again';
      
      case 'TIMEOUT_ERROR':
        return 'The request took too long, try again or increase timeout';
      
      case 'PARSING_ERROR':
        return 'The response format was unexpected, try again';
      
      case 'VALIDATION_ERROR':
        return 'Check your input parameters are correct';
      
      default:
        return 'An unexpected error occurred, please try again';
    }
  }
}

// Global error handler instance
export const globalErrorHandler = new AIErrorHandler();