import { 
  createReasoningAwareAdapter, 
  getOrCreateAdapter,
  globalAIProviderIntegration 
} from '../core/ai';
import { UnifiedModelAdapter } from '../core/ai/unified-adapter';

/**
 * 推理感知模型适配器配置
 * 
 * 这个配置文件展示了如何使用新的统一适配器系统
 * 来处理推理模型和普通模型
 */

// 推理模型配置映射
const REASONING_MODEL_CONFIGS = {
  // DeepSeek 推理模型
  'deepseek-reasoner': {
    provider: 'deepseek',
    reasoningConfig: {
      tagName: 'think',
      separator: '\n\n',
      startWithReasoning: true,
      enabled: true
    }
  },
  
  // OpenAI O1 系列
  'o1-preview': {
    provider: 'openai',
    reasoningConfig: {
      tagName: 'thinking',
      separator: '\n---\n',
      startWithReasoning: false,
      enabled: true
    }
  },
  
  'o1-mini': {
    provider: 'openai',
    reasoningConfig: {
      tagName: 'thinking',
      separator: '\n---\n',
      startWithReasoning: false,
      enabled: true
    }
  }
} as const;

// 普通模型配置
const STANDARD_MODEL_CONFIGS = {
  'gpt-4': { provider: 'openai' },
  'gpt-3.5-turbo': { provider: 'openai' },
  'claude-3-opus': { provider: 'claude' },
  'claude-3-sonnet': { provider: 'claude' },
  'gemini-pro': { provider: 'gemini' },
  'moonshot-v1-32k': { provider: 'moonshot' }
} as const;

/**
 * 适配器缓存管理
 */
class AdapterCache {
  private cache = new Map<string, UnifiedModelAdapter>();
  
  get(key: string): UnifiedModelAdapter | undefined {
    return this.cache.get(key);
  }
  
  set(key: string, adapter: UnifiedModelAdapter): void {
    this.cache.set(key);
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
}

const adapterCache = new AdapterCache();

/**
 * 智能适配器工厂
 * 根据模型类型自动创建合适的适配器
 */
export class SmartAdapterFactory {
  
  /**
   * 创建适配器（自动检测推理模型）
   */
  static async createAdapter(
    modelId: string,
    apiKey: string,
    endpoint?: string
  ): Promise<UnifiedModelAdapter> {
    const cacheKey = `${modelId}:${apiKey.slice(0, 8)}:${endpoint || 'default'}`;
    
    // 检查缓存
    const cachedAdapter = adapterCache.get(cacheKey);
    if (cachedAdapter) {
      return cachedAdapter;
    }

    let adapter: UnifiedModelAdapter;
    
    // 检查是否为推理模型
    if (modelId in REASONING_MODEL_CONFIGS) {
      const config = REASONING_MODEL_CONFIGS[modelId as keyof typeof REASONING_MODEL_CONFIGS];
      
      adapter = await createReasoningAwareAdapter(
        config.provider,
        apiKey,
        modelId,
        endpoint
      );
      
      // 应用推理配置
      adapter.updateConfig({
        reasoningConfig: config.reasoningConfig
      });
      
      console.log(`Created reasoning-aware adapter for ${modelId}`);
      
    } else if (modelId in STANDARD_MODEL_CONFIGS) {
      const config = STANDARD_MODEL_CONFIGS[modelId as keyof typeof STANDARD_MODEL_CONFIGS];
      
      adapter = await getOrCreateAdapter(
        config.provider,
        apiKey,
        modelId,
        endpoint
      );
      
      console.log(`Created standard adapter for ${modelId}`);
      
    } else {
      // 尝试自动检测提供商和推理能力
      const provider = this.detectProvider(modelId);
      
      adapter = await createReasoningAwareAdapter(
        provider,
        apiKey,
        modelId,
        endpoint
      );
      
      console.log(`Created auto-detected adapter for ${modelId} with provider ${provider}`);
    }
    
    // 缓存适配器
    adapterCache.set(cacheKey, adapter);
    
    return adapter;
  }

  /**
   * 批量创建适配器
   */
  static async createBatchAdapters(
    configs: Array<{
      modelId: string;
      apiKey: string;
      endpoint?: string;
    }>
  ): Promise<UnifiedModelAdapter[]> {
    const results = await Promise.allSettled(
      configs.map(config => 
        this.createAdapter(config.modelId, config.apiKey, config.endpoint)
      )
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`Failed to create adapter for ${configs[index].modelId}:`, result.reason);
        throw result.reason;
      }
    });
  }

  /**
   * 基于模型ID自动检测提供商
   */
  private static detectProvider(modelId: string): string {
    const modelIdLower = modelId.toLowerCase();
    
    if (modelIdLower.includes('gpt') || modelIdLower.includes('o1')) {
      return 'openai';
    } else if (modelIdLower.includes('claude')) {
      return 'claude';
    } else if (modelIdLower.includes('gemini')) {
      return 'gemini';
    } else if (modelIdLower.includes('moonshot')) {
      return 'moonshot';
    } else if (modelIdLower.includes('deepseek')) {
      return 'deepseek';
    } else if (modelIdLower.includes('llama')) {
      return 'ollama';
    } else {
      // 默认使用 OpenRouter 作为通用代理
      return 'openrouter';
    }
  }

  /**
   * 获取所有支持的推理模型
   */
  static getSupportedReasoningModels(): string[] {
    return Object.keys(REASONING_MODEL_CONFIGS);
  }

  /**
   * 获取所有支持的标准模型
   */
  static getSupportedStandardModels(): string[] {
    return Object.keys(STANDARD_MODEL_CONFIGS);
  }

  /**
   * 检查模型是否支持推理
   */
  static isReasoningModel(modelId: string): boolean {
    return modelId in REASONING_MODEL_CONFIGS;
  }

  /**
   * 清理适配器缓存
   */
  static clearCache(): void {
    adapterCache.clear();
  }

  /**
   * 获取缓存统计
   */
  static getCacheStats(): { size: number; models: string[] } {
    return {
      size: adapterCache.size(),
      models: Array.from(adapterCache['cache'].keys())
    };
  }
}

/**
 * 全局适配器管理器实例
 */
export const smartAdapterFactory = SmartAdapterFactory;

/**
 * 便捷函数：为表单填充任务创建优化的适配器
 */
export async function createFormFillingAdapter(
  modelId: string,
  apiKey: string,
  endpoint?: string
): Promise<UnifiedModelAdapter> {
  const adapter = await SmartAdapterFactory.createAdapter(modelId, apiKey, endpoint);
  
  // 为表单填充任务优化配置
  adapter.updateConfig({
    enableStructuredOutput: true,
    defaultOptions: {
      temperature: 0.3, // 较低的温度以获得更一致的结果
      maxTokens: 2000,   // 足够的token用于表单数据
    }
  });
  
  return adapter;
}

/**
 * 便捷函数：获取适配器信息用于调试
 */
export function getAdapterDebugInfo(adapter: UnifiedModelAdapter) {
  const info = adapter.getInfo();
  
  return {
    ...info,
    cacheStats: SmartAdapterFactory.getCacheStats(),
    supportedReasoningModels: SmartAdapterFactory.getSupportedReasoningModels(),
    supportedStandardModels: SmartAdapterFactory.getSupportedStandardModels()
  };
}