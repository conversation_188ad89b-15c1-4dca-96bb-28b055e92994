import { wrapLanguageModel, extractReasoningMiddleware, LanguageModel } from 'ai';
import { z } from 'zod';
import { AIModelError, GenerationOptions, GenerationResult } from './interfaces';

// 推理结果接口
export interface ReasoningResult {
  reasoning?: string;
  text: string;
  originalResult: GenerationResult;
}

// 推理中间件配置
export interface ReasoningMiddlewareConfig {
  tagName?: string;
  separator?: string;
  startWithReasoning?: boolean;
  enabled?: boolean;
}

// 默认推理中间件配置
const DEFAULT_REASONING_CONFIG: ReasoningMiddlewareConfig = {
  tagName: 'think',
  separator: '\n\n',
  startWithReasoning: true,
  enabled: true,
};

/**
 * 推理模型包装器类
 * 负责处理推理模型的思考过程提取和结构化输出
 */
export class ReasoningModelWrapper {
  private config: ReasoningMiddlewareConfig;
  private wrappedModel: LanguageModel | null = null;

  constructor(config: Partial<ReasoningMiddlewareConfig> = {}) {
    this.config = { ...DEFAULT_REASONING_CONFIG, ...config };
  }

  /**
   * 包装模型以支持推理提取
   */
  wrapModel(model: LanguageModel): LanguageModel {
    if (!this.config.enabled) {
      return model;
    }

    try {
      this.wrappedModel = wrapLanguageModel({
        model,
        middleware: extractReasoningMiddleware({
          tagName: this.config.tagName!,
          separator: this.config.separator!,
          startWithReasoning: this.config.startWithReasoning!,
        }),
      });
      
      return this.wrappedModel;
    } catch (error) {
      console.warn('Failed to wrap model with reasoning middleware:', error);
      // 如果包装失败，返回原始模型
      return model;
    }
  }

  /**
   * 检查模型是否支持推理功能
   */
  isReasoningModel(modelId: string): boolean {
    // 检查模型 ID 中是否包含推理模型的关键词
    const reasoningKeywords = [
      'reasoning',
      'reasoner',
      'think',
      'o1',
      'deepseek-reasoner',
      'qwen-reasoner'
    ];
    
    const modelIdLower = modelId.toLowerCase();
    return reasoningKeywords.some(keyword => modelIdLower.includes(keyword));
  }

  /**
   * 根据模型类型自动配置推理中间件
   */
  autoConfigureForModel(modelId: string): ReasoningMiddlewareConfig {
    const isReasoning = this.isReasoningModel(modelId);
    
    if (!isReasoning) {
      return { ...this.config, enabled: false };
    }

    // 根据不同模型调整配置
    let config = { ...this.config, enabled: true };
    
    if (modelId.includes('deepseek')) {
      config.tagName = 'think';
      config.startWithReasoning = true;
    } else if (modelId.includes('o1')) {
      config.tagName = 'thinking';
      config.startWithReasoning = false;
    }
    
    return config;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ReasoningMiddlewareConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ReasoningMiddlewareConfig {
    return { ...this.config };
  }
}

/**
 * 推理内容解析器
 * 用于处理推理模型的输出，提取思考过程和最终答案
 */
export class ReasoningContentParser {
  private config: ReasoningMiddlewareConfig;

  constructor(config: ReasoningMiddlewareConfig) {
    this.config = config;
  }

  /**
   * 解析推理内容，分离思考过程和最终答案
   */
  parseReasoningContent(content: string): { reasoning?: string; text: string } {
    if (!this.config.enabled) {
      return { text: content };
    }

    try {
      // 尝试提取思考标签内容
      const tagName = this.config.tagName || 'think';
      const tagRegex = new RegExp(`<${tagName}>(.*?)</${tagName}>`, 'gs');
      const matches = Array.from(content.matchAll(tagRegex));
      
      if (matches.length > 0) {
        // 提取所有思考内容
        const reasoningParts = matches.map(match => match[1].trim());
        const reasoning = reasoningParts.join(this.config.separator || '\n\n');
        
        // 移除思考标签，获取最终文本
        let text = content.replace(tagRegex, '').trim();
        
        // 如果配置了分隔符，尝试进一步清理
        if (this.config.separator) {
          // 移除多余的分隔符
          text = text.replace(new RegExp(`${this.config.separator}+`, 'g'), this.config.separator);
          text = text.trim();
        }
        
        return { reasoning, text };
      }
      
      // 如果没有找到思考标签，返回原始内容
      return { text: content };
    } catch (error) {
      console.warn('Failed to parse reasoning content:', error);
      return { text: content };
    }
  }

  /**
   * 验证解析结果是否有效
   */
  validateParsingResult(result: { reasoning?: string; text: string }): boolean {
    // 检查是否有有效的文本内容
    if (!result.text || result.text.trim().length === 0) {
      return false;
    }

    // 如果有推理内容，检查是否合理
    if (result.reasoning) {
      // 推理内容不应该为空或过短
      if (result.reasoning.trim().length < 10) {
        return false;
      }
    }

    return true;
  }

  /**
   * 清理和格式化最终文本
   */
  cleanFinalText(text: string): string {
    // 移除多余的空白字符
    let cleaned = text.replace(/\n{3,}/g, '\n\n').trim();
    
    // 移除可能残留的标签
    const tagName = this.config.tagName || 'think';
    cleaned = cleaned.replace(new RegExp(`</?${tagName}>`, 'g'), '');
    
    return cleaned.trim();
  }
}

/**
 * 创建推理模型包装器的工厂函数
 */
export function createReasoningWrapper(
  modelId: string,
  customConfig?: Partial<ReasoningMiddlewareConfig>
): ReasoningModelWrapper {
  const wrapper = new ReasoningModelWrapper(customConfig);
  
  // 根据模型自动配置
  const autoConfig = wrapper.autoConfigureForModel(modelId);
  wrapper.updateConfig(autoConfig);
  
  return wrapper;
}

/**
 * 全局推理处理器实例
 */
export const globalReasoningProcessor = new ReasoningModelWrapper();