import { Message, ContextWindow, AIModelError } from './interfaces';

// Token counting interface
export interface TokenCounter {
  countTokens(text: string, modelId?: string): number;
  estimateTokens(messages: Message[], modelId?: string): number;
}

// Simple token counter implementation (approximation)
export class SimpleTokenCounter implements TokenCounter {
  // Rough token estimation: ~4 characters per token for most models
  private readonly CHARS_PER_TOKEN = 4;
  
  // Model-specific adjustments
  private readonly MODEL_ADJUSTMENTS: Record<string, number> = {
    'gpt-4': 3.8,
    'gpt-3.5-turbo': 4.0,
    'claude': 3.5,
    'gemini': 4.2,
  };

  countTokens(text: string, modelId?: string): number {
    if (!text) return 0;
    
    const charsPerToken = this.getCharsPerToken(modelId);
    return Math.ceil(text.length / charsPerToken);
  }

  estimateTokens(messages: Message[], modelId?: string): number {
    let totalTokens = 0;
    
    for (const message of messages) {
      // Count message content
      totalTokens += this.countTokens(message.content, modelId);
      
      // Add overhead for message formatting (role, etc.)
      totalTokens += 4; // Rough estimate for formatting
    }
    
    // Add base conversation overhead
    totalTokens += 10;
    
    return totalTokens;
  }

  private getCharsPerToken(modelId?: string): number {
    if (!modelId) return this.CHARS_PER_TOKEN;
    
    for (const [model, ratio] of Object.entries(this.MODEL_ADJUSTMENTS)) {
      if (modelId.toLowerCase().includes(model)) {
        return ratio;
      }
    }
    
    return this.CHARS_PER_TOKEN;
  }
}

// Context window manager
export class ContextWindowManager {
  private tokenCounter: TokenCounter;

  constructor(tokenCounter: TokenCounter = new SimpleTokenCounter()) {
    this.tokenCounter = tokenCounter;
  }

  calculateContextWindow(
    messages: Message[],
    maxTokens: number,
    reservedForOutput: number = 1000,
    modelId?: string
  ): ContextWindow {
    const totalTokens = this.tokenCounter.estimateTokens(messages, modelId);
    const systemPromptTokens = this.getSystemPromptTokens(messages, modelId);
    
    return {
      maxTokens,
      reservedForOutput,
      systemPromptTokens,
      availableTokens: Math.max(0, maxTokens - reservedForOutput - totalTokens),
    };
  }

  validateContextSize(
    messages: Message[],
    maxTokens: number,
    reservedForOutput: number = 1000,
    modelId?: string
  ): { isValid: boolean; tokensUsed: number; tokensAvailable: number } {
    const tokensUsed = this.tokenCounter.estimateTokens(messages, modelId);
    const tokensAvailable = maxTokens - reservedForOutput;
    
    return {
      isValid: tokensUsed <= tokensAvailable,
      tokensUsed,
      tokensAvailable,
    };
  }

  trimMessages(
    messages: Message[],
    maxTokens: number,
    reservedForOutput: number = 1000,
    modelId?: string
  ): Message[] {
    const tokensAvailable = maxTokens - reservedForOutput;
    
    if (messages.length === 0) return [];
    
    // Always preserve system messages
    const systemMessages = messages.filter(m => m.role === 'system');
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    
    let trimmedMessages = [...systemMessages];
    let currentTokens = this.tokenCounter.estimateTokens(systemMessages, modelId);
    
    // Add non-system messages from newest to oldest until we hit the limit
    for (let i = nonSystemMessages.length - 1; i >= 0; i--) {
      const message = nonSystemMessages[i];
      const messageTokens = this.tokenCounter.countTokens(message.content, modelId) + 4; // +4 for formatting
      
      if (currentTokens + messageTokens <= tokensAvailable) {
        trimmedMessages.push(message);
        currentTokens += messageTokens;
      } else {
        // Try to include partial message content if it's the last user message
        if (message.role === 'user' && trimmedMessages.filter(m => m.role !== 'system').length === 0) {
          const availableForContent = tokensAvailable - currentTokens - 4;
          if (availableForContent > 100) { // Only if we have reasonable space
            const trimmedContent = this.trimText(message.content, availableForContent, modelId);
            trimmedMessages.push({
              ...message,
              content: trimmedContent,
            });
          }
        }
        break;
      }
    }
    
    // Restore chronological order (system messages first, then chronological)
    const finalSystemMessages = trimmedMessages.filter(m => m.role === 'system');
    const finalNonSystemMessages = trimmedMessages
      .filter(m => m.role !== 'system')
      .sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));
    
    return [...finalSystemMessages, ...finalNonSystemMessages];
  }

  private getSystemPromptTokens(messages: Message[], modelId?: string): number {
    const systemMessages = messages.filter(m => m.role === 'system');
    return this.tokenCounter.estimateTokens(systemMessages, modelId);
  }

  private trimText(text: string, maxTokens: number, modelId?: string): string {
    const charsPerToken = 4; // Rough estimate
    const maxChars = maxTokens * charsPerToken;
    
    if (text.length <= maxChars) return text;
    
    // Try to trim at sentence boundaries
    const trimmed = text.substring(0, maxChars);
    const lastSentence = trimmed.lastIndexOf('.');
    const lastQuestion = trimmed.lastIndexOf('?');
    const lastExclamation = trimmed.lastIndexOf('!');
    
    const lastPunctuation = Math.max(lastSentence, lastQuestion, lastExclamation);
    
    if (lastPunctuation > maxChars * 0.8) {
      return trimmed.substring(0, lastPunctuation + 1);
    }
    
    // Fall back to word boundaries
    const lastSpace = trimmed.lastIndexOf(' ');
    if (lastSpace > maxChars * 0.8) {
      return trimmed.substring(0, lastSpace) + '...';
    }
    
    // Hard cut with ellipsis
    return trimmed.substring(0, maxChars - 3) + '...';
  }
}

// Context strategy interface
export interface ContextStrategy {
  name: string;
  apply(messages: Message[], maxTokens: number, modelId?: string): Message[];
}

// Keep recent messages strategy
export class KeepRecentStrategy implements ContextStrategy {
  name = 'keep-recent';

  apply(messages: Message[], maxTokens: number, modelId?: string): Message[] {
    const manager = new ContextWindowManager();
    return manager.trimMessages(messages, maxTokens, 1000, modelId);
  }
}

// Summarize old messages strategy
export class SummarizeOldStrategy implements ContextStrategy {
  name = 'summarize-old';
  private summaryCache = new Map<string, string>();

  apply(messages: Message[], maxTokens: number, modelId?: string): Message[] {
    const manager = new ContextWindowManager();
    const validation = manager.validateContextSize(messages, maxTokens, 1000, modelId);
    
    if (validation.isValid) return messages;
    
    // Find the split point - keep recent messages, summarize old ones
    const systemMessages = messages.filter(m => m.role === 'system');
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    
    const targetTokens = maxTokens * 0.7; // Use 70% of available space
    let keepFromIndex = nonSystemMessages.length;
    let currentTokens = manager.tokenCounter.estimateTokens(systemMessages, modelId);
    
    // Find how many recent messages we can keep
    for (let i = nonSystemMessages.length - 1; i >= 0; i--) {
      const messageTokens = manager.tokenCounter.countTokens(nonSystemMessages[i].content, modelId) + 4;
      
      if (currentTokens + messageTokens <= targetTokens) {
        currentTokens += messageTokens;
        keepFromIndex = i;
      } else {
        break;
      }
    }
    
    if (keepFromIndex === 0) {
      // Can't summarize anything, fall back to recent strategy
      return new KeepRecentStrategy().apply(messages, maxTokens, modelId);
    }
    
    // Create summary of old messages
    const oldMessages = nonSystemMessages.slice(0, keepFromIndex);
    const recentMessages = nonSystemMessages.slice(keepFromIndex);
    
    const summaryKey = this.createSummaryKey(oldMessages);
    let summary = this.summaryCache.get(summaryKey);
    
    if (!summary) {
      summary = this.createSummary(oldMessages);
      this.summaryCache.set(summaryKey, summary);
    }
    
    const summaryMessage: Message = {
      role: 'system',
      content: `Previous conversation summary: ${summary}`,
      timestamp: Date.now(),
    };
    
    return [...systemMessages, summaryMessage, ...recentMessages];
  }

  private createSummaryKey(messages: Message[]): string {
    const content = messages.map(m => `${m.role}:${m.content.substring(0, 100)}`).join('|');
    return btoa(content).substring(0, 32);
  }

  private createSummary(messages: Message[]): string {
    // Simple extractive summary - take key points from each message
    const summaryParts: string[] = [];
    
    for (const message of messages) {
      if (message.role === 'user') {
        // Extract key intent from user messages
        const intent = this.extractUserIntent(message.content);
        if (intent) {
          summaryParts.push(`User asked: ${intent}`);
        }
      } else if (message.role === 'assistant') {
        // Extract key information from assistant responses
        const keyInfo = this.extractKeyInformation(message.content);
        if (keyInfo) {
          summaryParts.push(`Assistant provided: ${keyInfo}`);
        }
      }
    }
    
    return summaryParts.join('. ') || 'Previous conversation covered various topics.';
  }

  private extractUserIntent(content: string): string {
    // Simple heuristics to extract user intent
    const sentences = content.split(/[.!?]+/).map(s => s.trim()).filter(Boolean);
    
    // Look for questions
    const questions = sentences.filter(s => s.includes('?') || 
      s.toLowerCase().startsWith('what') || 
      s.toLowerCase().startsWith('how') || 
      s.toLowerCase().startsWith('why') || 
      s.toLowerCase().startsWith('when') || 
      s.toLowerCase().startsWith('where'));
    
    if (questions.length > 0) {
      return questions[0].substring(0, 100);
    }
    
    // Look for requests/commands
    const requests = sentences.filter(s => 
      s.toLowerCase().includes('please') ||
      s.toLowerCase().includes('can you') ||
      s.toLowerCase().includes('help me') ||
      s.toLowerCase().includes('need to'));
    
    if (requests.length > 0) {
      return requests[0].substring(0, 100);
    }
    
    // Fall back to first sentence
    return sentences[0]?.substring(0, 100) || '';
  }

  private extractKeyInformation(content: string): string {
    const sentences = content.split(/[.!?]+/).map(s => s.trim()).filter(Boolean);
    
    // Take first substantial sentence
    const substantialSentence = sentences.find(s => s.length > 20);
    return substantialSentence?.substring(0, 150) || '';
  }
}

// Context manager with multiple strategies
export class ContextManager {
  private strategies = new Map<string, ContextStrategy>();
  private windowManager: ContextWindowManager;

  constructor(tokenCounter?: TokenCounter) {
    this.windowManager = new ContextWindowManager(tokenCounter);
    
    // Register default strategies
    this.registerStrategy(new KeepRecentStrategy());
    this.registerStrategy(new SummarizeOldStrategy());
  }

  registerStrategy(strategy: ContextStrategy): void {
    this.strategies.set(strategy.name, strategy);
  }

  async manageContext(
    messages: Message[],
    maxTokens: number,
    strategyName: string = 'keep-recent',
    modelId?: string
  ): Promise<Message[]> {
    const strategy = this.strategies.get(strategyName);
    if (!strategy) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: `Context strategy '${strategyName}' not found`,
        retryable: false,
      });
    }

    try {
      return strategy.apply(messages, maxTokens, modelId);
    } catch (error) {
      console.warn(`Context strategy ${strategyName} failed, falling back to keep-recent`);
      const fallbackStrategy = this.strategies.get('keep-recent');
      return fallbackStrategy!.apply(messages, maxTokens, modelId);
    }
  }

  validateContext(
    messages: Message[],
    maxTokens: number,
    reservedForOutput: number = 1000,
    modelId?: string
  ): { isValid: boolean; tokensUsed: number; tokensAvailable: number } {
    return this.windowManager.validateContextSize(messages, maxTokens, reservedForOutput, modelId);
  }

  calculateContextWindow(
    messages: Message[],
    maxTokens: number,
    reservedForOutput: number = 1000,
    modelId?: string
  ): ContextWindow {
    return this.windowManager.calculateContextWindow(messages, maxTokens, reservedForOutput, modelId);
  }
}

// Global context manager instance
export const contextManager = new ContextManager();