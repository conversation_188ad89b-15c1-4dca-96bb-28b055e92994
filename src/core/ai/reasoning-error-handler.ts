import { AIModelError, AIErrorType } from './interfaces';
import { ReasoningMiddlewareConfig } from './reasoning-middleware';

/**
 * 错误恢复策略类型
 */
export enum RecoveryStrategy {
  // 立即重试
  IMMEDIATE_RETRY = 'immediate_retry',
  // 降级推理功能
  DISABLE_REASONING = 'disable_reasoning',
  // 降级结构化输出
  DISABLE_STRUCTURED_OUTPUT = 'disable_structured_output',
  // 降级到备用模型
  FALLBACK_MODEL = 'fallback_model',
  // 使用缓存响应
  USE_CACHED_RESPONSE = 'use_cached_response',
  // 完全失败
  COMPLETE_FAILURE = 'complete_failure'
}

/**
 * 错误恢复配置
 */
export interface RecoveryConfig {
  // 最大重试次数
  maxRetries: number;
  // 重试间隔（毫秒）
  retryInterval: number;
  // 启用的恢复策略
  enabledStrategies: RecoveryStrategy[];
  // 备用模型配置
  fallbackModels?: string[];
  // 缓存时长（毫秒）
  cacheTimeout?: number;
}

/**
 * 错误上下文信息
 */
export interface ErrorContext {
  modelId: string;
  provider: string;
  isReasoningModel: boolean;
  attemptNumber: number;
  originalError: any;
  userPrompt?: string;
  enabledFeatures: {
    reasoning: boolean;
    structuredOutput: boolean;
  };
  timestamp: number;
}

/**
 * 恢复操作结果
 */
export interface RecoveryResult {
  success: boolean;
  strategy: RecoveryStrategy;
  newConfig?: {
    reasoningEnabled?: boolean;
    structuredOutputEnabled?: boolean;
    modelId?: string;
    provider?: string;
  };
  message?: string;
  shouldRetry: boolean;
}

/**
 * 推理模型错误处理器
 * 专门处理推理模型相关的错误和恢复策略
 */
export class ReasoningErrorHandler {
  private recoveryConfig: RecoveryConfig;
  private errorHistory: Map<string, ErrorContext[]> = new Map();
  private responseCache: Map<string, any> = new Map();

  constructor(config: Partial<RecoveryConfig> = {}) {
    this.recoveryConfig = {
      maxRetries: 3,
      retryInterval: 1000,
      enabledStrategies: [
        RecoveryStrategy.DISABLE_REASONING,
        RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT,
        RecoveryStrategy.IMMEDIATE_RETRY,
        RecoveryStrategy.COMPLETE_FAILURE
      ],
      cacheTimeout: 300000, // 5分钟
      ...config
    };
  }

  /**
   * 处理错误并决定恢复策略
   */
  async handleError(
    error: AIModelError | Error,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    // 记录错误历史
    this.recordError(context);

    // 分析错误类型
    const errorAnalysis = this.analyzeError(error);
    
    // 检查是否达到最大重试次数
    if (context.attemptNumber >= this.recoveryConfig.maxRetries) {
      return {
        success: false,
        strategy: RecoveryStrategy.COMPLETE_FAILURE,
        message: `Maximum retry attempts (${this.recoveryConfig.maxRetries}) exceeded`,
        shouldRetry: false
      };
    }

    // 根据错误类型和上下文选择恢复策略
    const strategy = this.selectRecoveryStrategy(errorAnalysis, context);
    
    // 执行恢复策略
    return this.executeRecoveryStrategy(strategy, context, errorAnalysis);
  }

  /**
   * 分析错误类型和严重程度
   */
  private analyzeError(error: AIModelError | Error): {
    type: AIErrorType;
    severity: 'low' | 'medium' | 'high';
    isRetryable: boolean;
    likelyReasoningRelated: boolean;
    likelyStructuredOutputRelated: boolean;
  } {
    if (error instanceof AIModelError) {
      return {
        type: error.type,
        severity: this.getErrorSeverity(error.type),
        isRetryable: error.retryable,
        likelyReasoningRelated: this.isReasoningRelatedError(error),
        likelyStructuredOutputRelated: this.isStructuredOutputRelatedError(error)
      };
    }

    // 通用错误处理
    return {
      type: 'UNKNOWN_ERROR',
      severity: 'medium',
      isRetryable: true,
      likelyReasoningRelated: this.hasReasoningKeywords(error.message),
      likelyStructuredOutputRelated: this.hasStructuredOutputKeywords(error.message)
    };
  }

  /**
   * 获取错误严重程度
   */
  private getErrorSeverity(errorType: AIErrorType): 'low' | 'medium' | 'high' {
    const severityMap: Record<AIErrorType, 'low' | 'medium' | 'high'> = {
      'CONFIGURATION_ERROR': 'high',
      'AUTHENTICATION_ERROR': 'high',
      'RATE_LIMIT_ERROR': 'medium',
      'CONTEXT_LENGTH_ERROR': 'medium',
      'MODEL_UNAVAILABLE_ERROR': 'high',
      'NETWORK_ERROR': 'medium',
      'PARSING_ERROR': 'low',
      'VALIDATION_ERROR': 'low',
      'TIMEOUT_ERROR': 'medium',
      'UNKNOWN_ERROR': 'medium'
    };

    return severityMap[errorType] || 'medium';
  }

  /**
   * 检查是否为推理相关错误
   */
  private isReasoningRelatedError(error: AIModelError): boolean {
    const reasoningKeywords = [
      'think', 'thinking', 'reason', 'reasoning',
      'middleware', 'parsing', 'extraction'
    ];
    
    return this.hasReasoningKeywords(error.message) || 
           (error.originalError && this.hasReasoningKeywords(String(error.originalError)));
  }

  /**
   * 检查是否为结构化输出相关错误
   */
  private isStructuredOutputRelatedError(error: AIModelError): boolean {
    return error.type === 'PARSING_ERROR' || 
           error.type === 'VALIDATION_ERROR' ||
           this.hasStructuredOutputKeywords(error.message);
  }

  /**
   * 检查消息中是否包含推理关键词
   */
  private hasReasoningKeywords(message: string): boolean {
    const keywords = ['think>', 'thinking>', 'reason', 'middleware', 'extraction'];
    return keywords.some(keyword => message.toLowerCase().includes(keyword));
  }

  /**
   * 检查消息中是否包含结构化输出关键词
   */
  private hasStructuredOutputKeywords(message: string): boolean {
    const keywords = ['json', 'parse', 'validation', 'schema', 'structured'];
    return keywords.some(keyword => message.toLowerCase().includes(keyword));
  }

  /**
   * 选择恢复策略
   */
  private selectRecoveryStrategy(
    errorAnalysis: ReturnType<ReasoningErrorHandler['analyzeError']>,
    context: ErrorContext
  ): RecoveryStrategy {
    const { type, severity, isRetryable, likelyReasoningRelated, likelyStructuredOutputRelated } = errorAnalysis;
    const { enabledFeatures, attemptNumber } = context;

    // 高严重程度错误，直接失败
    if (severity === 'high' && attemptNumber > 1) {
      return RecoveryStrategy.COMPLETE_FAILURE;
    }

    // 推理相关错误，尝试禁用推理功能
    if (likelyReasoningRelated && enabledFeatures.reasoning) {
      if (this.recoveryConfig.enabledStrategies.includes(RecoveryStrategy.DISABLE_REASONING)) {
        return RecoveryStrategy.DISABLE_REASONING;
      }
    }

    // 结构化输出相关错误，尝试禁用结构化输出
    if (likelyStructuredOutputRelated && enabledFeatures.structuredOutput) {
      if (this.recoveryConfig.enabledStrategies.includes(RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT)) {
        return RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT;
      }
    }

    // 可重试的错误，进行立即重试
    if (isRetryable && attemptNumber === 1) {
      if (this.recoveryConfig.enabledStrategies.includes(RecoveryStrategy.IMMEDIATE_RETRY)) {
        return RecoveryStrategy.IMMEDIATE_RETRY;
      }
    }

    // 备用模型策略
    if (this.recoveryConfig.fallbackModels && this.recoveryConfig.fallbackModels.length > 0) {
      if (this.recoveryConfig.enabledStrategies.includes(RecoveryStrategy.FALLBACK_MODEL)) {
        return RecoveryStrategy.FALLBACK_MODEL;
      }
    }

    // 默认策略
    return RecoveryStrategy.COMPLETE_FAILURE;
  }

  /**
   * 执行恢复策略
   */
  private async executeRecoveryStrategy(
    strategy: RecoveryStrategy,
    context: ErrorContext,
    errorAnalysis: ReturnType<ReasoningErrorHandler['analyzeError']>
  ): Promise<RecoveryResult> {
    console.log(`Executing recovery strategy: ${strategy} for model ${context.modelId}`);

    switch (strategy) {
      case RecoveryStrategy.IMMEDIATE_RETRY:
        // 等待重试间隔
        await this.delay(this.recoveryConfig.retryInterval);
        return {
          success: true,
          strategy,
          message: 'Retrying with same configuration',
          shouldRetry: true
        };

      case RecoveryStrategy.DISABLE_REASONING:
        return {
          success: true,
          strategy,
          newConfig: {
            reasoningEnabled: false
          },
          message: 'Disabled reasoning functionality due to processing errors',
          shouldRetry: true
        };

      case RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT:
        return {
          success: true,
          strategy,
          newConfig: {
            structuredOutputEnabled: false
          },
          message: 'Disabled structured output due to parsing errors',
          shouldRetry: true
        };

      case RecoveryStrategy.FALLBACK_MODEL:
        const fallbackModel = this.selectFallbackModel(context);
        if (fallbackModel) {
          return {
            success: true,
            strategy,
            newConfig: {
              modelId: fallbackModel.modelId,
              provider: fallbackModel.provider,
              reasoningEnabled: false, // 默认禁用推理功能
              structuredOutputEnabled: true
            },
            message: `Switched to fallback model: ${fallbackModel.modelId}`,
            shouldRetry: true
          };
        }
        break;

      case RecoveryStrategy.USE_CACHED_RESPONSE:
        const cachedResponse = this.getCachedResponse(context);
        if (cachedResponse) {
          return {
            success: true,
            strategy,
            message: 'Using cached response',
            shouldRetry: false
          };
        }
        break;

      case RecoveryStrategy.COMPLETE_FAILURE:
      default:
        return {
          success: false,
          strategy,
          message: `Recovery failed: ${errorAnalysis.type}`,
          shouldRetry: false
        };
    }

    // 如果策略执行失败，返回完全失败
    return {
      success: false,
      strategy: RecoveryStrategy.COMPLETE_FAILURE,
      message: 'Recovery strategy execution failed',
      shouldRetry: false
    };
  }

  /**
   * 选择备用模型
   */
  private selectFallbackModel(context: ErrorContext): { modelId: string; provider: string } | null {
    if (!this.recoveryConfig.fallbackModels || this.recoveryConfig.fallbackModels.length === 0) {
      return null;
    }

    // 避免选择已经失败的模型
    const errorHistory = this.getErrorHistory(context.modelId);
    const failedModels = new Set(errorHistory.map(e => e.modelId));

    const availableFallbacks = this.recoveryConfig.fallbackModels.filter(
      model => !failedModels.has(model)
    );

    if (availableFallbacks.length === 0) {
      return null;
    }

    // 简单选择第一个可用的备用模型
    const fallbackModelId = availableFallbacks[0];
    
    // 根据模型ID推断提供商（这里可以改进为配置化）
    const provider = this.inferProviderFromModelId(fallbackModelId);

    return { modelId: fallbackModelId, provider };
  }

  /**
   * 根据模型ID推断提供商
   */
  private inferProviderFromModelId(modelId: string): string {
    const modelIdLower = modelId.toLowerCase();
    
    if (modelIdLower.includes('gpt')) return 'openai';
    if (modelIdLower.includes('claude')) return 'claude';
    if (modelIdLower.includes('gemini')) return 'gemini';
    if (modelIdLower.includes('deepseek')) return 'deepseek';
    if (modelIdLower.includes('moonshot')) return 'moonshot';
    
    return 'openrouter'; // 默认使用openrouter
  }

  /**
   * 获取缓存响应
   */
  private getCachedResponse(context: ErrorContext): any | null {
    const cacheKey = this.generateCacheKey(context);
    const cached = this.responseCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < (this.recoveryConfig.cacheTimeout || 300000)) {
      return cached.response;
    }
    
    return null;
  }

  /**
   * 缓存响应
   */
  setCachedResponse(context: ErrorContext, response: any): void {
    const cacheKey = this.generateCacheKey(context);
    this.responseCache.set(cacheKey, {
      response,
      timestamp: Date.now()
    });
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(context: ErrorContext): string {
    const prompt = context.userPrompt || '';
    const promptHash = this.simpleHash(prompt);
    return `${context.modelId}:${context.provider}:${promptHash}`;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 记录错误历史
   */
  private recordError(context: ErrorContext): void {
    const key = `${context.modelId}:${context.provider}`;
    const history = this.errorHistory.get(key) || [];
    
    history.push(context);
    
    // 只保留最近的100个错误记录
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
    
    this.errorHistory.set(key, history);
  }

  /**
   * 获取错误历史
   */
  private getErrorHistory(modelId: string): ErrorContext[] {
    const histories = Array.from(this.errorHistory.values()).flat();
    return histories.filter(h => h.modelId === modelId);
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新恢复配置
   */
  updateConfig(newConfig: Partial<RecoveryConfig>): void {
    this.recoveryConfig = { ...this.recoveryConfig, ...newConfig };
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByModel: Record<string, number>;
    errorsByType: Record<string, number>;
    recoverySuccessRate: number;
  } {
    const allErrors = Array.from(this.errorHistory.values()).flat();
    
    const errorsByModel: Record<string, number> = {};
    const errorsByType: Record<string, number> = {};
    
    allErrors.forEach(error => {
      const modelKey = `${error.modelId}:${error.provider}`;
      errorsByModel[modelKey] = (errorsByModel[modelKey] || 0) + 1;
    });

    return {
      totalErrors: allErrors.length,
      errorsByModel,
      errorsByType,
      recoverySuccessRate: 0.85 // 这里可以根据实际恢复成功率计算
    };
  }

  /**
   * 清理过期的缓存和错误历史
   */
  cleanup(): void {
    const now = Date.now();
    const cacheTimeout = this.recoveryConfig.cacheTimeout || 300000;
    
    // 清理过期缓存
    for (const [key, cached] of this.responseCache.entries()) {
      if (now - cached.timestamp > cacheTimeout) {
        this.responseCache.delete(key);
      }
    }

    // 清理旧的错误历史（保留最近24小时）
    const dayAgo = now - (24 * 60 * 60 * 1000);
    for (const [key, history] of this.errorHistory.entries()) {
      const recentHistory = history.filter(h => h.timestamp > dayAgo);
      if (recentHistory.length === 0) {
        this.errorHistory.delete(key);
      } else {
        this.errorHistory.set(key, recentHistory);
      }
    }
  }
}

/**
 * 全局推理错误处理器实例
 */
export const globalReasoningErrorHandler = new ReasoningErrorHandler({
  maxRetries: 3,
  retryInterval: 2000,
  enabledStrategies: [
    RecoveryStrategy.DISABLE_REASONING,
    RecoveryStrategy.DISABLE_STRUCTURED_OUTPUT,
    RecoveryStrategy.IMMEDIATE_RETRY,
    RecoveryStrategy.COMPLETE_FAILURE
  ],
  fallbackModels: [
    'gpt-3.5-turbo',
    'claude-3-haiku',
    'gemini-pro'
  ],
  cacheTimeout: 300000
});