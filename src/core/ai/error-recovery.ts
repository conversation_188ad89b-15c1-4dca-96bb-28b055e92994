import { z } from 'zod';
import { AIModelError, AIErrorType, AIErrorTypeSchema, GenerationOptions, GenerationResult } from './interfaces';

// 重试策略配置
export const RetryConfigSchema = z.object({
  maxAttempts: z.number().min(1).max(10).default(3),
  baseDelay: z.number().min(100).default(1000), // 基础延迟（毫秒）
  maxDelay: z.number().min(1000).default(30000), // 最大延迟
  backoffStrategy: z.enum(['linear', 'exponential', 'fixed']).default('exponential'),
  jitter: z.boolean().default(true), // 添加随机抖动
  retryableErrors: z.array(AIErrorTypeSchema).default([
    'RATE_LIMIT_ERROR',
    'NETWORK_ERROR',
    'TIMEOUT_ERROR',
    'MODEL_UNAVAILABLE_ERROR',
  ]),
});
export type RetryConfig = z.infer<typeof RetryConfigSchema>;

// 降级策略配置
export const FallbackConfigSchema = z.object({
  enabled: z.boolean().default(true),
  fallbackModels: z.array(z.string()).default([]),
  fallbackTimeout: z.number().min(5000).default(15000),
  maxFallbackAttempts: z.number().min(1).max(5).default(2),
  preserveOptions: z.boolean().default(true), // 是否保持原始生成选项
  fallbackTriggers: z.array(AIErrorTypeSchema).default([
    'MODEL_UNAVAILABLE_ERROR',
    'CONTEXT_LENGTH_ERROR',
    'AUTHENTICATION_ERROR',
  ]),
});
export type FallbackConfig = z.infer<typeof FallbackConfigSchema>;

// 断路器配置
export const CircuitBreakerConfigSchema = z.object({
  enabled: z.boolean().default(true),
  failureThreshold: z.number().min(1).default(5), // 失败阈值
  recoveryTimeout: z.number().min(10000).default(60000), // 恢复超时
  halfOpenMaxCalls: z.number().min(1).default(3), // 半开状态最大调用数
  monitorWindow: z.number().min(30000).default(300000), // 监控窗口（5分钟）
});
export type CircuitBreakerConfig = z.infer<typeof CircuitBreakerConfigSchema>;

// 错误恢复上下文
export interface ErrorRecoveryContext {
  originalRequest: {
    modelId: string;
    providerId: string;
    messages: any[];
    options: GenerationOptions;
  };
  error: AIModelError;
  attempt: number;
  totalAttempts: number;
  startTime: number;
  lastAttemptTime: number;
}

// 恢复策略结果
export interface RecoveryResult {
  action: 'retry' | 'fallback' | 'fail';
  delay?: number;
  newModelId?: string;
  newOptions?: GenerationOptions;
  message?: string;
}

/**
 * 智能重试器 - 实现多种退避策略和错误分析
 */
export class SmartRetryHandler {
  private config: RetryConfig;
  
  constructor(config: Partial<RetryConfig> = {}) {
    this.config = RetryConfigSchema.parse(config);
  }

  /**
   * 执行带重试的操作
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: Partial<ErrorRecoveryContext> = {}
  ): Promise<T> {
    let lastError: AIModelError;
    const startTime = Date.now();
    
    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof AIModelError ? error : AIModelError.fromUnknown(error);
        
        // 检查是否为可重试的错误
        if (!this.isRetryableError(lastError)) {
          throw lastError;
        }
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === this.config.maxAttempts) {
          throw lastError;
        }

        // 计算延迟并等待
        const delay = this.calculateDelay(attempt);
        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, lastError.message);
        
        await this.delay(delay);
      }
    }
    
    throw lastError!;
  }

  /**
   * 检查错误是否可重试
   */
  private isRetryableError(error: AIModelError): boolean {
    return this.config.retryableErrors.includes(error.type) && error.retryable;
  }

  /**
   * 计算重试延迟
   */
  private calculateDelay(attempt: number): number {
    let delay: number;
    
    switch (this.config.backoffStrategy) {
      case 'linear':
        delay = this.config.baseDelay * attempt;
        break;
      case 'exponential':
        delay = this.config.baseDelay * Math.pow(2, attempt - 1);
        break;
      case 'fixed':
        delay = this.config.baseDelay;
        break;
      default:
        delay = this.config.baseDelay;
    }
    
    // 应用最大延迟限制
    delay = Math.min(delay, this.config.maxDelay);
    
    // 添加抖动
    if (this.config.jitter) {
      const jitterRange = delay * 0.1;
      delay += (Math.random() - 0.5) * jitterRange;
    }
    
    return Math.floor(delay);
  }

  /**
   * 延迟执行
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<RetryConfig>): void {
    this.config = RetryConfigSchema.parse({ ...this.config, ...newConfig });
  }
}

/**
 * 降级处理器 - 实现模型降级和选项调整
 */
export class FallbackHandler {
  private config: FallbackConfig;
  
  constructor(config: Partial<FallbackConfig> = {}) {
    this.config = FallbackConfigSchema.parse(config);
  }

  /**
   * 获取降级策略
   */
  getFallbackStrategy(context: ErrorRecoveryContext): RecoveryResult {
    if (!this.config.enabled) {
      return { action: 'fail', message: 'Fallback is disabled' };
    }

    if (!this.shouldTriggerFallback(context.error)) {
      return { action: 'retry', message: 'Error type not configured for fallback' };
    }

    // 获取降级模型
    const fallbackModel = this.selectFallbackModel(context.originalRequest.modelId);
    if (!fallbackModel) {
      return { action: 'fail', message: 'No fallback models available' };
    }

    // 调整生成选项
    const newOptions = this.adjustOptionsForFallback(
      context.originalRequest.options,
      context.error
    );

    return {
      action: 'fallback',
      newModelId: fallbackModel,
      newOptions,
      message: `Falling back to model: ${fallbackModel}`,
    };
  }

  /**
   * 检查是否应该触发降级
   */
  private shouldTriggerFallback(error: AIModelError): boolean {
    return this.config.fallbackTriggers.includes(error.type);
  }

  /**
   * 选择降级模型
   */
  private selectFallbackModel(currentModelId: string): string | undefined {
    // 排除当前模型，选择第一个可用的降级模型
    const availableModels = this.config.fallbackModels.filter(
      model => model !== currentModelId
    );
    
    return availableModels[0];
  }

  /**
   * 为降级调整生成选项
   */
  private adjustOptionsForFallback(
    originalOptions: GenerationOptions,
    error: AIModelError
  ): GenerationOptions {
    if (!this.config.preserveOptions) {
      return originalOptions;
    }

    const adjustedOptions = { ...originalOptions };

    // 根据错误类型调整选项
    switch (error.type) {
      case 'CONTEXT_LENGTH_ERROR':
        // 减少最大 token 数
        if (adjustedOptions.maxTokens) {
          adjustedOptions.maxTokens = Math.floor(adjustedOptions.maxTokens * 0.8);
        }
        break;
        
      case 'RATE_LIMIT_ERROR':
        // 降低温度，使输出更确定性，可能减少处理时间
        if (adjustedOptions.temperature) {
          adjustedOptions.temperature = Math.max(0.1, adjustedOptions.temperature * 0.7);
        }
        break;
        
      case 'MODEL_UNAVAILABLE_ERROR':
        // 禁用一些高级功能
        adjustedOptions.enableStreaming = false;
        adjustedOptions.enableStructuredOutput = false;
        break;
    }

    return adjustedOptions;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<FallbackConfig>): void {
    this.config = FallbackConfigSchema.parse({ ...this.config, ...newConfig });
  }
}

/**
 * 断路器状态
 */
enum CircuitState {
  CLOSED = 'closed',     // 正常状态
  OPEN = 'open',         // 断路状态
  HALF_OPEN = 'half-open' // 半开状态
}

/**
 * 断路器 - 防止级联失败
 */
export class CircuitBreaker {
  private config: CircuitBreakerConfig;
  private state: CircuitState = CircuitState.CLOSED;
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private halfOpenCalls: number = 0;
  private failureHistory: number[] = [];

  constructor(private modelId: string, config: Partial<CircuitBreakerConfig> = {}) {
    this.config = CircuitBreakerConfigSchema.parse(config);
  }

  /**
   * 执行带断路器保护的操作
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (!this.config.enabled) {
      return operation();
    }

    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.halfOpenCalls = 0;
      } else {
        throw new AIModelError({
          type: 'MODEL_UNAVAILABLE_ERROR',
          message: `Circuit breaker is open for model: ${this.modelId}`,
          retryable: false,
        });
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * 处理成功调用
   */
  private onSuccess(): void {
    this.failures = 0;
    this.halfOpenCalls = 0;
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
    }
  }

  /**
   * 处理失败调用
   */
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    this.recordFailure();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
    } else if (this.failures >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
    }
  }

  /**
   * 记录失败历史
   */
  private recordFailure(): void {
    const now = Date.now();
    this.failureHistory.push(now);
    
    // 清理超出监控窗口的记录
    const cutoff = now - this.config.monitorWindow;
    this.failureHistory = this.failureHistory.filter(time => time > cutoff);
  }

  /**
   * 判断是否应该尝试重置
   */
  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime > this.config.recoveryTimeout;
  }

  /**
   * 获取断路器状态
   */
  getState(): { state: CircuitState; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }

  /**
   * 重置断路器
   */
  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failures = 0;
    this.lastFailureTime = 0;
    this.halfOpenCalls = 0;
    this.failureHistory = [];
  }
}

/**
 * 综合错误恢复管理器
 */
export class ErrorRecoveryManager {
  private retryHandler: SmartRetryHandler;
  private fallbackHandler: FallbackHandler;
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();

  constructor(
    retryConfig: Partial<RetryConfig> = {},
    fallbackConfig: Partial<FallbackConfig> = {},
    circuitBreakerConfig: Partial<CircuitBreakerConfig> = {}
  ) {
    this.retryHandler = new SmartRetryHandler(retryConfig);
    this.fallbackHandler = new FallbackHandler(fallbackConfig);
    
    // 为断路器配置提供默认值
    this.circuitBreakerConfig = circuitBreakerConfig;
  }

  private circuitBreakerConfig: Partial<CircuitBreakerConfig>;

  /**
   * 执行带完整错误恢复的操作
   */
  async executeWithRecovery<T>(
    operation: (modelId: string, options: GenerationOptions) => Promise<T>,
    context: ErrorRecoveryContext
  ): Promise<T> {
    const circuitBreaker = this.getCircuitBreaker(context.originalRequest.modelId);
    
    return circuitBreaker.execute(async () => {
      return this.retryHandler.executeWithRetry(async () => {
        try {
          return await operation(
            context.originalRequest.modelId,
            context.originalRequest.options
          );
        } catch (error) {
          const aiError = error instanceof AIModelError ? error : AIModelError.fromUnknown(error);
          
          // 尝试降级策略
          const recoveryResult = this.fallbackHandler.getFallbackStrategy({
            ...context,
            error: aiError,
          });

          if (recoveryResult.action === 'fallback' && recoveryResult.newModelId) {
            console.warn(`Executing fallback strategy: ${recoveryResult.message}`);
            
            // 使用降级模型重新执行
            const fallbackCircuitBreaker = this.getCircuitBreaker(recoveryResult.newModelId);
            return fallbackCircuitBreaker.execute(() =>
              operation(
                recoveryResult.newModelId!,
                recoveryResult.newOptions || context.originalRequest.options
              )
            );
          }
          
          throw aiError;
        }
      }, context);
    });
  }

  /**
   * 获取或创建断路器
   */
  private getCircuitBreaker(modelId: string): CircuitBreaker {
    if (!this.circuitBreakers.has(modelId)) {
      this.circuitBreakers.set(
        modelId,
        new CircuitBreaker(modelId, this.circuitBreakerConfig)
      );
    }
    return this.circuitBreakers.get(modelId)!;
  }

  /**
   * 获取所有断路器状态
   */
  getCircuitBreakerStates(): Record<string, any> {
    const states: Record<string, any> = {};
    for (const [modelId, breaker] of this.circuitBreakers.entries()) {
      states[modelId] = breaker.getState();
    }
    return states;
  }

  /**
   * 重置指定模型的断路器
   */
  resetCircuitBreaker(modelId: string): void {
    const breaker = this.circuitBreakers.get(modelId);
    if (breaker) {
      breaker.reset();
    }
  }

  /**
   * 重置所有断路器
   */
  resetAllCircuitBreakers(): void {
    for (const breaker of this.circuitBreakers.values()) {
      breaker.reset();
    }
  }

  /**
   * 更新配置
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryHandler.updateConfig(config);
  }

  updateFallbackConfig(config: Partial<FallbackConfig>): void {
    this.fallbackHandler.updateConfig(config);
  }
}

/**
 * 创建默认的错误恢复管理器
 */
export function createDefaultRecoveryManager(): ErrorRecoveryManager {
  return new ErrorRecoveryManager(
    {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffStrategy: 'exponential',
      jitter: true,
    },
    {
      enabled: true,
      fallbackModels: ['gpt-3.5-turbo', 'claude-3-sonnet-20240229'],
      maxFallbackAttempts: 2,
      preserveOptions: true,
    },
    {
      enabled: true,
      failureThreshold: 5,
      recoveryTimeout: 60000,
    }
  );
}

/**
 * 全局错误恢复管理器实例
 */
export const globalRecoveryManager = createDefaultRecoveryManager();