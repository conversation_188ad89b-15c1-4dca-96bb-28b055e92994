import { AIModelError, TokenUsage } from './interfaces';

// Performance metrics interface
export interface PerformanceMetrics {
  requestId: string;
  timestamp: number;
  provider: string;
  model: string;
  operation: 'generate' | 'stream' | 'structured';
  
  // Timing metrics
  startTime: number;
  endTime: number;
  duration: number;
  
  // Token metrics
  usage?: TokenUsage;
  
  // Success/failure
  success: boolean;
  error?: string;
  
  // Additional metadata
  metadata?: Record<string, any>;
}

// Metrics collector interface
export interface MetricsCollector {
  collect(metrics: PerformanceMetrics): void;
  getMetrics(filters?: MetricsFilter): PerformanceMetrics[];
  getStats(): MetricsStats;
  clear(): void;
}

export interface MetricsFilter {
  provider?: string;
  model?: string;
  operation?: string;
  startTime?: number;
  endTime?: number;
  success?: boolean;
}

export interface MetricsStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageDuration: number;
  totalTokensUsed: number;
  requestsByProvider: Record<string, number>;
  requestsByModel: Record<string, number>;
  errorsByType: Record<string, number>;
}

// In-memory metrics collector
export class MemoryMetricsCollector implements MetricsCollector {
  private metrics: PerformanceMetrics[] = [];
  private readonly maxMetrics: number;

  constructor(maxMetrics: number = 1000) {
    this.maxMetrics = maxMetrics;
  }

  collect(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  getMetrics(filters?: MetricsFilter): PerformanceMetrics[] {
    if (!filters) return [...this.metrics];
    
    return this.metrics.filter(metric => {
      if (filters.provider && metric.provider !== filters.provider) return false;
      if (filters.model && metric.model !== filters.model) return false;
      if (filters.operation && metric.operation !== filters.operation) return false;
      if (filters.startTime && metric.timestamp < filters.startTime) return false;
      if (filters.endTime && metric.timestamp > filters.endTime) return false;
      if (filters.success !== undefined && metric.success !== filters.success) return false;
      return true;
    });
  }

  getStats(): MetricsStats {
    const totalRequests = this.metrics.length;
    const successfulRequests = this.metrics.filter(m => m.success).length;
    const failedRequests = totalRequests - successfulRequests;
    
    const durations = this.metrics.map(m => m.duration);
    const averageDuration = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;
    
    const totalTokensUsed = this.metrics.reduce((sum, m) => {
      return sum + (m.usage?.totalTokens || 0);
    }, 0);
    
    const requestsByProvider: Record<string, number> = {};
    const requestsByModel: Record<string, number> = {};
    const errorsByType: Record<string, number> = {};
    
    for (const metric of this.metrics) {
      requestsByProvider[metric.provider] = (requestsByProvider[metric.provider] || 0) + 1;
      requestsByModel[metric.model] = (requestsByModel[metric.model] || 0) + 1;
      
      if (!metric.success && metric.error) {
        errorsByType[metric.error] = (errorsByType[metric.error] || 0) + 1;
      }
    }
    
    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageDuration,
      totalTokensUsed,
      requestsByProvider,
      requestsByModel,
      errorsByType,
    };
  }

  clear(): void {
    this.metrics = [];
  }
}

// Performance monitor for tracking AI operations
export class PerformanceMonitor {
  private collector: MetricsCollector;
  private activeOperations = new Map<string, {
    startTime: number;
    provider: string;
    model: string;
    operation: 'generate' | 'stream' | 'structured';
    metadata?: Record<string, any>;
  }>();

  constructor(collector: MetricsCollector = new MemoryMetricsCollector()) {
    this.collector = collector;
  }

  startOperation(
    requestId: string,
    provider: string,
    model: string,
    operation: 'generate' | 'stream' | 'structured',
    metadata?: Record<string, any>
  ): void {
    this.activeOperations.set(requestId, {
      startTime: Date.now(),
      provider,
      model,
      operation,
      metadata,
    });
  }

  endOperation(
    requestId: string,
    success: boolean,
    usage?: TokenUsage,
    error?: string
  ): void {
    const operation = this.activeOperations.get(requestId);
    if (!operation) {
      console.warn(`No active operation found for request ${requestId}`);
      return;
    }

    const endTime = Date.now();
    const metrics: PerformanceMetrics = {
      requestId,
      timestamp: operation.startTime,
      provider: operation.provider,
      model: operation.model,
      operation: operation.operation,
      startTime: operation.startTime,
      endTime,
      duration: endTime - operation.startTime,
      usage,
      success,
      error,
      metadata: operation.metadata,
    };

    this.collector.collect(metrics);
    this.activeOperations.delete(requestId);
  }

  getMetrics(filters?: MetricsFilter): PerformanceMetrics[] {
    return this.collector.getMetrics(filters);
  }

  getStats(): MetricsStats {
    return this.collector.getStats();
  }

  getProviderComparison(): Record<string, {
    averageDuration: number;
    successRate: number;
    totalRequests: number;
  }> {
    const stats = this.collector.getStats();
    const comparison: Record<string, any> = {};
    
    for (const [provider, requestCount] of Object.entries(stats.requestsByProvider)) {
      const providerMetrics = this.collector.getMetrics({ provider });
      const successfulRequests = providerMetrics.filter(m => m.success).length;
      const averageDuration = providerMetrics.reduce((sum, m) => sum + m.duration, 0) / providerMetrics.length;
      
      comparison[provider] = {
        averageDuration: Math.round(averageDuration),
        successRate: Math.round((successfulRequests / requestCount) * 100) / 100,
        totalRequests: requestCount,
      };
    }
    
    return comparison;
  }

  clear(): void {
    this.collector.clear();
    this.activeOperations.clear();
  }
}

// Resource monitor for tracking system resources
export class ResourceMonitor {
  private memoryUsage: number[] = [];
  private readonly maxSamples = 100;
  private monitoringInterval?: NodeJS.Timeout;

  startMonitoring(intervalMs: number = 5000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMemoryUsage();
    }, intervalMs);
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  private collectMemoryUsage(): void {
    let memoryMB = 0;

    if (typeof performance !== 'undefined' && 'memory' in performance) {
      // Chrome-specific memory API
      const memory = (performance as any).memory;
      memoryMB = memory.usedJSHeapSize / 1024 / 1024;
    } else if (typeof process !== 'undefined' && process.memoryUsage) {
      // Node.js memory usage
      const usage = process.memoryUsage();
      memoryMB = usage.heapUsed / 1024 / 1024;
    }

    this.memoryUsage.push(memoryMB);
    
    if (this.memoryUsage.length > this.maxSamples) {
      this.memoryUsage = this.memoryUsage.slice(-this.maxSamples);
    }
  }

  getMemoryStats(): {
    current: number;
    average: number;
    peak: number;
    samples: number;
  } {
    if (this.memoryUsage.length === 0) {
      return { current: 0, average: 0, peak: 0, samples: 0 };
    }

    const current = this.memoryUsage[this.memoryUsage.length - 1];
    const average = this.memoryUsage.reduce((sum, usage) => sum + usage, 0) / this.memoryUsage.length;
    const peak = Math.max(...this.memoryUsage);

    return {
      current: Math.round(current * 100) / 100,
      average: Math.round(average * 100) / 100,
      peak: Math.round(peak * 100) / 100,
      samples: this.memoryUsage.length,
    };
  }

  isMemoryPressure(thresholdMB: number = 100): boolean {
    const stats = this.getMemoryStats();
    return stats.current > thresholdMB || stats.average > thresholdMB * 0.8;
  }
}

// Health checker for monitoring system health
export class HealthChecker {
  private checks = new Map<string, () => Promise<{ healthy: boolean; message?: string }>>();
  private lastCheckResults = new Map<string, { healthy: boolean; message?: string; timestamp: number }>();

  addCheck(name: string, checkFn: () => Promise<{ healthy: boolean; message?: string }>): void {
    this.checks.set(name, checkFn);
  }

  removeCheck(name: string): void {
    this.checks.delete(name);
    this.lastCheckResults.delete(name);
  }

  async runChecks(): Promise<Record<string, { healthy: boolean; message?: string; timestamp: number }>> {
    const results: Record<string, any> = {};

    for (const [name, checkFn] of this.checks) {
      try {
        const result = await checkFn();
        const resultWithTimestamp = {
          ...result,
          timestamp: Date.now(),
        };
        
        results[name] = resultWithTimestamp;
        this.lastCheckResults.set(name, resultWithTimestamp);
      } catch (error) {
        const errorResult = {
          healthy: false,
          message: error instanceof Error ? error.message : String(error),
          timestamp: Date.now(),
        };
        
        results[name] = errorResult;
        this.lastCheckResults.set(name, errorResult);
      }
    }

    return results;
  }

  getLastResults(): Record<string, { healthy: boolean; message?: string; timestamp: number }> {
    const results: Record<string, any> = {};
    for (const [name, result] of this.lastCheckResults) {
      results[name] = result;
    }
    return results;
  }

  isHealthy(): boolean {
    for (const result of this.lastCheckResults.values()) {
      if (!result.healthy) return false;
    }
    return true;
  }
}

// Global monitoring instances
export const performanceMonitor = new PerformanceMonitor();
export const resourceMonitor = new ResourceMonitor();
export const healthChecker = new HealthChecker();

// Setup default health checks
healthChecker.addCheck('memory', async () => {
  const memoryStats = resourceMonitor.getMemoryStats();
  const isHighUsage = memoryStats.current > 200; // 200MB threshold
  
  return {
    healthy: !isHighUsage,
    message: isHighUsage ? `High memory usage: ${memoryStats.current}MB` : undefined,
  };
});

healthChecker.addCheck('performance', async () => {
  const stats = performanceMonitor.getStats();
  const successRate = stats.totalRequests > 0 ? stats.successfulRequests / stats.totalRequests : 1;
  const isLowSuccessRate = successRate < 0.8; // 80% threshold
  
  return {
    healthy: !isLowSuccessRate,
    message: isLowSuccessRate ? `Low success rate: ${Math.round(successRate * 100)}%` : undefined,
  };
});