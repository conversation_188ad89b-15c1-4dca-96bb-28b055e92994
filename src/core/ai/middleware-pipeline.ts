import { z } from 'zod';
import { Message, GenerationOptions, GenerationResult, AIModelError } from './interfaces';

// 中间件上下文接口
export interface MiddlewareContext {
  messages: Message[];
  options: GenerationOptions;
  metadata: Record<string, any>;
  modelId: string;
  providerId: string;
}

// 中间件执行结果
export interface MiddlewareResult {
  messages?: Message[];
  options?: GenerationOptions;
  metadata?: Record<string, any>;
  skipNext?: boolean;  // 是否跳过后续中间件
  error?: Error;
}

// 响应中间件上下文
export interface ResponseMiddlewareContext {
  result: GenerationResult;
  originalContext: MiddlewareContext;
  metadata: Record<string, any>;
}

// 响应中间件结果
export interface ResponseMiddlewareResult {
  result?: GenerationResult;
  metadata?: Record<string, any>;
  error?: Error;
}

// 中间件接口
export interface RequestMiddleware {
  name: string;
  order: number;
  execute(context: MiddlewareContext): Promise<MiddlewareResult>;
}

export interface ResponseMiddleware {
  name: string;
  order: number;
  execute(context: ResponseMiddlewareContext): Promise<ResponseMiddlewareResult>;
}

// 中间件配置
export const MiddlewareConfigSchema = z.object({
  enabled: z.boolean().default(true),
  order: z.number().default(0),
  options: z.record(z.any()).optional(),
});
export type MiddlewareConfig = z.infer<typeof MiddlewareConfigSchema>;

/**
 * 推理控制中间件 - 处理推理模型的特殊请求格式
 */
export class ReasoningControlMiddleware implements RequestMiddleware {
  name = 'reasoning-control';
  order = 100;

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const { options, metadata } = context;
    
    if (!options.enableReasoning) {
      // 明确禁用推理时，添加禁用标记
      return {
        metadata: {
          ...metadata,
          reasoningDisabled: true,
          disableThinking: true,
        },
      };
    }

    // 启用推理时，配置推理参数
    const reasoningConfig = options.reasoningConfig || {};
    return {
      metadata: {
        ...metadata,
        reasoningEnabled: true,
        reasoningConfig,
      },
    };
  }
}

/**
 * 请求验证中间件 - 验证请求参数的合法性
 */
export class RequestValidationMiddleware implements RequestMiddleware {
  name = 'request-validation';
  order = 50;

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const { messages, options } = context;

    // 验证消息数组
    if (!messages || messages.length === 0) {
      return {
        error: new AIModelError({
          type: 'VALIDATION_ERROR',
          message: 'Messages array cannot be empty',
          retryable: false,
        }),
      };
    }

    // 验证生成选项
    if (options.maxTokens && options.maxTokens < 1) {
      return {
        error: new AIModelError({
          type: 'VALIDATION_ERROR',
          message: 'maxTokens must be greater than 0',
          retryable: false,
        }),
      };
    }

    // 验证温度参数
    if (options.temperature !== undefined && (options.temperature < 0 || options.temperature > 2)) {
      return {
        error: new AIModelError({
          type: 'VALIDATION_ERROR',
          message: 'Temperature must be between 0 and 2',
          retryable: false,
        }),
      };
    }

    return {}; // 验证通过，不修改上下文
  }
}

/**
 * 上下文长度管理中间件 - 自动截断过长的上下文
 */
export class ContextLengthMiddleware implements RequestMiddleware {
  name = 'context-length';
  order = 200;

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const { messages, options, modelId } = context;
    
    // 简单的 token 估算（实际应该使用 tokenizer）
    const estimateTokens = (text: string): number => {
      return Math.ceil(text.length / 4);
    };

    const totalTokens = messages.reduce((sum, msg) => sum + estimateTokens(msg.content), 0);
    const maxContextTokens = this.getMaxContextTokens(modelId);
    const reservedTokens = options.maxTokens || 2048;
    const availableTokens = maxContextTokens - reservedTokens;

    if (totalTokens <= availableTokens) {
      return {}; // 不需要截断
    }

    // 截断策略：保留系统消息，截断历史对话
    const systemMessages = messages.filter(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    
    let currentTokens = systemMessages.reduce((sum, msg) => sum + estimateTokens(msg.content), 0);
    const trimmedConversation: Message[] = [];

    // 从最新消息开始保留
    for (let i = conversationMessages.length - 1; i >= 0; i--) {
      const msg = conversationMessages[i];
      const msgTokens = estimateTokens(msg.content);
      
      if (currentTokens + msgTokens <= availableTokens) {
        trimmedConversation.unshift(msg);
        currentTokens += msgTokens;
      } else {
        break;
      }
    }

    const trimmedMessages = [...systemMessages, ...trimmedConversation];

    return {
      messages: trimmedMessages,
      metadata: {
        ...context.metadata,
        originalMessageCount: messages.length,
        trimmedMessageCount: trimmedMessages.length,
        estimatedTokens: currentTokens,
      },
    };
  }

  private getMaxContextTokens(modelId: string): number {
    // 简化的模型上下文长度映射
    const contextLengths: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-4-turbo': 128000,
      'gpt-3.5-turbo': 16385,
      'claude-3-opus': 200000,
      'claude-3-sonnet': 200000,
      'gemini-pro': 32768,
    };

    for (const [model, length] of Object.entries(contextLengths)) {
      if (modelId.includes(model)) {
        return length;
      }
    }

    return 4096; // 默认值
  }
}

/**
 * 推理内容解析中间件 - 处理推理模型的响应
 */
export class ReasoningParsingMiddleware implements ResponseMiddleware {
  name = 'reasoning-parsing';
  order = 100;

  async execute(context: ResponseMiddlewareContext): Promise<ResponseMiddlewareResult> {
    const { result, originalContext } = context;

    // 检查是否为推理模型
    if (!originalContext.metadata.reasoningEnabled) {
      return {}; // 不是推理模型，直接返回
    }

    try {
      const parsed = this.parseReasoningContent(result.content);
      
      return {
        result: {
          ...result,
          content: parsed.text,
        },
        metadata: {
          ...context.metadata,
          reasoning: parsed.reasoning,
          hasReasoning: !!parsed.reasoning,
        },
      };
    } catch (error) {
      console.warn('Failed to parse reasoning content:', error);
      return {}; // 解析失败，返回原始结果
    }
  }

  private parseReasoningContent(content: string): { reasoning?: string; text: string } {
    // 支持多种推理标签格式
    const reasoningPatterns = [
      /<think>(.*?)<\/think>/gs,
      /<thinking>(.*?)<\/thinking>/gs,
      /<reasoning>(.*?)<\/reasoning>/gs,
      /\[REASONING\](.*?)\[\/REASONING\]/gs,
    ];

    for (const pattern of reasoningPatterns) {
      const matches = Array.from(content.matchAll(pattern));
      if (matches.length > 0) {
        const reasoning = matches.map(match => match[1].trim()).join('\n\n');
        const text = content.replace(pattern, '').trim();
        return { reasoning, text };
      }
    }

    return { text: content };
  }
}

/**
 * 结构化输出验证中间件 - 验证和修复结构化输出
 */
export class StructuredOutputMiddleware implements ResponseMiddleware {
  name = 'structured-output';
  order = 200;

  async execute(context: ResponseMiddlewareContext): Promise<ResponseMiddlewareResult> {
    const { result, originalContext } = context;
    
    if (!originalContext.options.enableStructuredOutput || !originalContext.options.responseSchema) {
      return {}; // 不需要结构化输出验证
    }

    try {
      // 尝试解析 JSON
      const jsonData = this.extractJSON(result.content);
      if (!jsonData) {
        return {
          error: new AIModelError({
            type: 'PARSING_ERROR',
            message: 'Failed to extract valid JSON from response',
            retryable: true,
          }),
        };
      }

      // 使用 Zod schema 验证
      const schema = originalContext.options.responseSchema;
      const validated = schema.parse(jsonData);

      return {
        result: {
          ...result,
          content: JSON.stringify(validated),
        },
        metadata: {
          ...context.metadata,
          structuredOutput: validated,
          validationPassed: true,
        },
      };
    } catch (error) {
      return {
        error: new AIModelError({
          type: 'VALIDATION_ERROR',
          message: `Structured output validation failed: ${error instanceof Error ? error.message : String(error)}`,
          retryable: true,
          originalError: error,
        }),
      };
    }
  }

  private extractJSON(content: string): any {
    // 尝试多种 JSON 提取策略
    const jsonPatterns = [
      /```json\s*(\{.*?\})\s*```/gs,
      /```\s*(\{.*?\})\s*```/gs,
      /(\{.*?\})/gs,
    ];

    for (const pattern of jsonPatterns) {
      const match = content.match(pattern);
      if (match) {
        try {
          return JSON.parse(match[1]);
        } catch {
          continue;
        }
      }
    }

    // 直接尝试解析整个内容
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  }
}

/**
 * 中间件管道 - 管理和执行中间件链
 */
export class MiddlewarePipeline {
  private requestMiddlewares: RequestMiddleware[] = [];
  private responseMiddlewares: ResponseMiddleware[] = [];

  /**
   * 注册请求中间件
   */
  addRequestMiddleware(middleware: RequestMiddleware): void {
    this.requestMiddlewares.push(middleware);
    this.requestMiddlewares.sort((a, b) => a.order - b.order);
  }

  /**
   * 注册响应中间件
   */
  addResponseMiddleware(middleware: ResponseMiddleware): void {
    this.responseMiddlewares.push(middleware);
    this.responseMiddlewares.sort((a, b) => a.order - b.order);
  }

  /**
   * 移除中间件
   */
  removeMiddleware(name: string): void {
    this.requestMiddlewares = this.requestMiddlewares.filter(m => m.name !== name);
    this.responseMiddlewares = this.responseMiddlewares.filter(m => m.name !== name);
  }

  /**
   * 执行请求中间件链
   */
  async executeRequestPipeline(context: MiddlewareContext): Promise<MiddlewareContext> {
    let currentContext = { ...context };

    for (const middleware of this.requestMiddlewares) {
      try {
        const result = await middleware.execute(currentContext);

        if (result.error) {
          throw result.error;
        }

        if (result.skipNext) {
          break;
        }

        // 更新上下文
        currentContext = {
          ...currentContext,
          messages: result.messages || currentContext.messages,
          options: result.options || currentContext.options,
          metadata: { ...currentContext.metadata, ...result.metadata },
        };
      } catch (error) {
        throw new AIModelError({
          type: 'UNKNOWN_ERROR',
          message: `Middleware ${middleware.name} failed: ${error instanceof Error ? error.message : String(error)}`,
          retryable: false,
          originalError: error,
        });
      }
    }

    return currentContext;
  }

  /**
   * 执行响应中间件链
   */
  async executeResponsePipeline(
    result: GenerationResult,
    originalContext: MiddlewareContext
  ): Promise<{ result: GenerationResult; metadata: Record<string, any> }> {
    let currentResult = { ...result };
    let currentMetadata: Record<string, any> = {};

    for (const middleware of this.responseMiddlewares) {
      try {
        const responseResult = await middleware.execute({
          result: currentResult,
          originalContext,
          metadata: currentMetadata,
        });

        if (responseResult.error) {
          throw responseResult.error;
        }

        // 更新结果和元数据
        currentResult = responseResult.result || currentResult;
        currentMetadata = { ...currentMetadata, ...responseResult.metadata };
      } catch (error) {
        throw new AIModelError({
          type: 'UNKNOWN_ERROR',
          message: `Response middleware ${middleware.name} failed: ${error instanceof Error ? error.message : String(error)}`,
          retryable: false,
          originalError: error,
        });
      }
    }

    return { result: currentResult, metadata: currentMetadata };
  }

  /**
   * 获取已注册的中间件列表
   */
  getMiddlewares(): { request: string[]; response: string[] } {
    return {
      request: this.requestMiddlewares.map(m => m.name),
      response: this.responseMiddlewares.map(m => m.name),
    };
  }
}

/**
 * 创建默认中间件管道
 */
export function createDefaultPipeline(): MiddlewarePipeline {
  const pipeline = new MiddlewarePipeline();

  // 添加默认请求中间件
  pipeline.addRequestMiddleware(new RequestValidationMiddleware());
  pipeline.addRequestMiddleware(new ReasoningControlMiddleware());
  pipeline.addRequestMiddleware(new ContextLengthMiddleware());

  // 添加默认响应中间件
  pipeline.addResponseMiddleware(new ReasoningParsingMiddleware());
  pipeline.addResponseMiddleware(new StructuredOutputMiddleware());

  return pipeline;
}

/**
 * 全局默认管道实例
 */
export const defaultPipeline = createDefaultPipeline();