import { z } from 'zod';

// Core message types
export const MessageRoleSchema = z.enum(['system', 'user', 'assistant']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

export const MessageSchema = z.object({
  role: MessageRoleSchema,
  content: z.string(),
  timestamp: z.number().optional(),
});
export type Message = z.infer<typeof MessageSchema>;

// Token usage information
export const TokenUsageSchema = z.object({
  promptTokens: z.number().min(0),
  completionTokens: z.number().min(0),
  totalTokens: z.number().min(0),
});
export type TokenUsage = z.infer<typeof TokenUsageSchema>;

// Model capabilities
export const ModelCapabilitiesSchema = z.object({
  supportsStreaming: z.boolean(),
  supportsStructuredOutput: z.boolean(),
  supportsSystemPrompt: z.boolean(),
  supportsMultimodal: z.boolean().optional(),
  maxContextTokens: z.number().min(1),
  maxOutputTokens: z.number().min(1),
});
export type ModelCapabilities = z.infer<typeof ModelCapabilitiesSchema>;

// Model information
export const ModelInfoSchema = z.object({
  id: z.string(),
  name: z.string(),
  provider: z.string(),
  capabilities: ModelCapabilitiesSchema,
  isAvailable: z.boolean().optional(),
  pricing: z.object({
    inputCostPer1kTokens: z.number().min(0),
    outputCostPer1kTokens: z.number().min(0),
    currency: z.string().default('USD'),
  }).optional(),
});
export type ModelInfo = z.infer<typeof ModelInfoSchema>;

// Generation options
export const GenerationOptionsSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).optional(),
  topP: z.number().min(0).max(1).optional(),
  topK: z.number().min(1).optional(),
  presencePenalty: z.number().min(-2).max(2).optional(),
  frequencyPenalty: z.number().min(-2).max(2).optional(),
  stopSequences: z.array(z.string()).optional(),
  enableStreaming: z.boolean().optional(),
  enableStructuredOutput: z.boolean().optional(),
  responseSchema: z.any().optional(), // Zod schema for structured output
  // Reasoning options
  enableReasoning: z.boolean().optional(),
  reasoningConfig: z.object({
    tagName: z.string().optional(),
    separator: z.string().optional(),
    startWithReasoning: z.boolean().optional(),
  }).optional(),
});
export type GenerationOptions = z.infer<typeof GenerationOptionsSchema>;

// Generation result
export const GenerationResultSchema = z.object({
  content: z.string(),
  usage: TokenUsageSchema,
  finishReason: z.enum(['stop', 'length', 'error', 'cancelled']).optional(),
  metadata: z.record(z.any()).optional(),
});
export type GenerationResult = z.infer<typeof GenerationResultSchema>;

// Reasoning result (extends GenerationResult)
export const ReasoningResultSchema = z.object({
  reasoning: z.string().optional(),
  text: z.string(),
  originalResult: GenerationResultSchema,
});
export type ReasoningResult = z.infer<typeof ReasoningResultSchema>;

// Streaming chunk
export const StreamChunkSchema = z.object({
  delta: z.string(),
  isComplete: z.boolean(),
  usage: TokenUsageSchema.optional(),
  metadata: z.record(z.any()).optional(),
});
export type StreamChunk = z.infer<typeof StreamChunkSchema>;

// Abstract AI Model interface
export interface AIModel {
  readonly info: ModelInfo;
  
  // Lifecycle
  initialize(): Promise<void>;
  isReady(): Promise<boolean>;
  dispose(): Promise<void>;
  
  // Generation
  generateText(
    messages: Message[], 
    options?: GenerationOptions
  ): Promise<GenerationResult>;
  
  generateStream(
    messages: Message[], 
    options?: GenerationOptions
  ): AsyncIterable<StreamChunk>;
  
  // Structured output
  generateStructured<T>(
    messages: Message[], 
    schema: z.ZodSchema<T>, 
    options?: GenerationOptions
  ): Promise<{ data: T; result: GenerationResult }>;
}

// Provider configuration
export const ProviderConfigSchema = z.object({
  apiKey: z.string(),
  endpoint: z.string().url().optional(),
  model: z.string().optional(),
  defaultOptions: GenerationOptionsSchema.optional(),
  timeout: z.number().min(1000).optional(),
  retries: z.number().min(0).max(5).optional(),
  metadata: z.record(z.any()).optional(),
});
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;

// Provider factory interface
export interface AIProviderFactory {
  readonly name: string;
  readonly supportedModels: string[];
  
  // Model management
  createModel(modelId: string, config: ProviderConfig): Promise<AIModel>;
  listAvailableModels(config: ProviderConfig): Promise<ModelInfo[]>;
  validateConfig(config: ProviderConfig): Promise<boolean>;
  
  // Provider info
  getCapabilities(modelId: string): ModelCapabilities;
  estimateTokens(text: string, modelId?: string): number;
}

// Context management
export const ContextWindowSchema = z.object({
  maxTokens: z.number().min(1),
  reservedForOutput: z.number().min(0),
  systemPromptTokens: z.number().min(0),
  availableTokens: z.number().min(0),
});
export type ContextWindow = z.infer<typeof ContextWindowSchema>;

export interface ContextManager {
  calculateTokens(messages: Message[], modelId: string): Promise<number>;
  trimMessages(
    messages: Message[], 
    maxTokens: number, 
    modelId: string
  ): Promise<Message[]>;
  validateContextSize(
    messages: Message[], 
    maxTokens: number, 
    modelId: string
  ): Promise<boolean>;
}

// Error types
export const AIErrorTypeSchema = z.enum([
  'CONFIGURATION_ERROR',
  'AUTHENTICATION_ERROR', 
  'RATE_LIMIT_ERROR',
  'CONTEXT_LENGTH_ERROR',
  'MODEL_UNAVAILABLE_ERROR',
  'NETWORK_ERROR',
  'PARSING_ERROR',
  'VALIDATION_ERROR',
  'TIMEOUT_ERROR',
  'UNKNOWN_ERROR'
]);
export type AIErrorType = z.infer<typeof AIErrorTypeSchema>;

export const AIErrorSchema = z.object({
  type: AIErrorTypeSchema,
  message: z.string(),
  code: z.string().optional(),
  statusCode: z.number().optional(),
  retryable: z.boolean(),
  fallbackOptions: z.array(z.string()).optional(),
  originalError: z.any().optional(),
});
export type AIError = z.infer<typeof AIErrorSchema>;

export class AIModelError extends Error {
  public readonly type: AIErrorType;
  public readonly retryable: boolean;
  public readonly fallbackOptions?: string[];
  public readonly statusCode?: number;
  public readonly originalError?: any;

  constructor(error: AIError) {
    super(error.message);
    this.name = 'AIModelError';
    this.type = error.type;
    this.retryable = error.retryable;
    this.fallbackOptions = error.fallbackOptions;
    this.statusCode = error.statusCode;
    this.originalError = error.originalError;
  }

  static fromUnknown(error: unknown, type: AIErrorType = 'UNKNOWN_ERROR'): AIModelError {
    const message = error instanceof Error ? error.message : String(error);
    return new AIModelError({
      type,
      message,
      retryable: type !== 'CONFIGURATION_ERROR' && type !== 'AUTHENTICATION_ERROR',
      originalError: error,
    });
  }
}