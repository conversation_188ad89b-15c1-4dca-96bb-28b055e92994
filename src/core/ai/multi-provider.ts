import { 
  AIProviderFactory, 
  AIModel, 
  ModelInfo, 
  ProviderConfig, 
  ModelCapabilities,
  AIModelError,
  Message,
  GenerationOptions,
  GenerationResult
} from './interfaces';
import { BaseAIModel } from './base-model';
import { aiProviderRegistry } from './factory';

// Multi-provider manager for handling multiple AI providers
export class MultiProviderManager {
  private primaryProvider?: string;
  private fallbackProviders: string[] = [];
  private providerConfigs = new Map<string, ProviderConfig>();
  private modelCache = new Map<string, AIModel>();

  constructor() {}

  // Configuration methods
  setPrimaryProvider(providerName: string, config: ProviderConfig): void {
    this.primaryProvider = providerName;
    this.providerConfigs.set(providerName, config);
  }

  addFallbackProvider(providerName: string, config: ProviderConfig): void {
    this.fallbackProviders.push(providerName);
    this.providerConfigs.set(providerName, config);
  }

  setProviderConfig(providerName: string, config: ProviderConfig): void {
    this.providerConfigs.set(providerName, config);
  }

  getProviderConfig(providerName: string): ProviderConfig | undefined {
    return this.providerConfigs.get(providerName);
  }

  // Model creation and management
  async createModel(modelId: string, preferredProvider?: string): Promise<AIModel> {
    const cacheKey = `${preferredProvider || this.primaryProvider}:${modelId}`;
    
    // Check cache first
    if (this.modelCache.has(cacheKey)) {
      const cachedModel = this.modelCache.get(cacheKey)!;
      if (await cachedModel.isReady()) {
        return cachedModel;
      }
      // Remove stale model from cache
      this.modelCache.delete(cacheKey);
    }

    const providers = this.getProviderPriority(preferredProvider);
    
    for (const providerName of providers) {
      const config = this.providerConfigs.get(providerName);
      if (!config) continue;

      try {
        const model = await aiProviderRegistry.createModel(providerName, modelId, config);
        await model.initialize();
        
        // Cache successful model
        this.modelCache.set(cacheKey, model);
        return model;
      } catch (error) {
        console.warn(`Failed to create model ${modelId} with provider ${providerName}:`, error);
        continue;
      }
    }

    throw new AIModelError({
      type: 'MODEL_UNAVAILABLE_ERROR',
      message: `Failed to create model ${modelId} with any available provider`,
      retryable: false,
    });
  }

  async listAvailableModels(providerName?: string): Promise<Map<string, ModelInfo[]>> {
    const modelsByProvider = new Map<string, ModelInfo[]>();
    const providersToCheck = providerName 
      ? [providerName] 
      : this.getAllConfiguredProviders();

    for (const provider of providersToCheck) {
      const config = this.providerConfigs.get(provider);
      if (!config) continue;

      try {
        const models = await aiProviderRegistry.listAvailableModels(provider, config);
        modelsByProvider.set(provider, models);
      } catch (error) {
        console.warn(`Failed to list models for provider ${provider}:`, error);
        modelsByProvider.set(provider, []);
      }
    }

    return modelsByProvider;
  }

  async getBestAvailableModel(requirements?: {
    supportsStreaming?: boolean;
    supportsStructuredOutput?: boolean;
    minContextTokens?: number;
  }): Promise<{ provider: string; model: ModelInfo } | null> {
    const allModels = await this.listAvailableModels();
    
    for (const [provider, models] of allModels) {
      for (const model of models) {
        if (this.modelMeetsRequirements(model, requirements)) {
          return { provider, model };
        }
      }
    }

    return null;
  }

  // Provider management
  private getProviderPriority(preferredProvider?: string): string[] {
    const priority: string[] = [];
    
    if (preferredProvider && this.providerConfigs.has(preferredProvider)) {
      priority.push(preferredProvider);
    }
    
    if (this.primaryProvider && !priority.includes(this.primaryProvider)) {
      priority.push(this.primaryProvider);
    }
    
    for (const fallback of this.fallbackProviders) {
      if (!priority.includes(fallback)) {
        priority.push(fallback);
      }
    }
    
    return priority;
  }

  private getAllConfiguredProviders(): string[] {
    return Array.from(this.providerConfigs.keys());
  }

  private modelMeetsRequirements(
    model: ModelInfo, 
    requirements?: {
      supportsStreaming?: boolean;
      supportsStructuredOutput?: boolean;
      minContextTokens?: number;
    }
  ): boolean {
    if (!requirements) return true;

    const caps = model.capabilities;
    
    if (requirements.supportsStreaming && !caps.supportsStreaming) return false;
    if (requirements.supportsStructuredOutput && !caps.supportsStructuredOutput) return false;
    if (requirements.minContextTokens && caps.maxContextTokens < requirements.minContextTokens) return false;
    
    return true;
  }

  // Cleanup
  async dispose(): Promise<void> {
    const disposePromises = Array.from(this.modelCache.values()).map(model => model.dispose());
    await Promise.all(disposePromises);
    this.modelCache.clear();
  }
}

// Provider load balancer for distributing requests
export class ProviderLoadBalancer {
  private providerStats = new Map<string, {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastRequestTime: number;
  }>();

  constructor(private multiProviderManager: MultiProviderManager) {}

  async selectOptimalProvider(modelId: string, requirements?: any): Promise<string> {
    const availableModels = await this.multiProviderManager.listAvailableModels();
    const candidates: { provider: string; score: number }[] = [];

    for (const [provider, models] of availableModels) {
      const hasModel = models.some(m => m.id === modelId);
      if (!hasModel) continue;

      const stats = this.providerStats.get(provider);
      const score = this.calculateProviderScore(provider, stats);
      candidates.push({ provider, score });
    }

    if (candidates.length === 0) {
      throw new AIModelError({
        type: 'MODEL_UNAVAILABLE_ERROR',
        message: `No providers available for model ${modelId}`,
        retryable: false,
      });
    }

    // Sort by score (higher is better)
    candidates.sort((a, b) => b.score - a.score);
    return candidates[0].provider;
  }

  recordRequest(provider: string, success: boolean, responseTimeMs: number): void {
    const stats = this.providerStats.get(provider) || {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastRequestTime: 0,
    };

    stats.totalRequests++;
    stats.lastRequestTime = Date.now();

    if (success) {
      stats.successfulRequests++;
    } else {
      stats.failedRequests++;
    }

    // Update rolling average response time
    stats.averageResponseTime = 
      (stats.averageResponseTime * (stats.totalRequests - 1) + responseTimeMs) / stats.totalRequests;

    this.providerStats.set(provider, stats);
  }

  private calculateProviderScore(provider: string, stats?: any): number {
    if (!stats || stats.totalRequests === 0) {
      return 0.5; // Neutral score for unknown providers
    }

    const successRate = stats.successfulRequests / stats.totalRequests;
    const responseTimeFactor = Math.max(0, 1 - (stats.averageResponseTime / 30000)); // Penalty for slow responses
    const recencyFactor = this.calculateRecencyFactor(stats.lastRequestTime);

    // Weighted score: success rate (60%) + response time (25%) + recency (15%)
    return (successRate * 0.6) + (responseTimeFactor * 0.25) + (recencyFactor * 0.15);
  }

  private calculateRecencyFactor(lastRequestTime: number): number {
    const timeSinceLastRequest = Date.now() - lastRequestTime;
    const hoursSince = timeSinceLastRequest / (1000 * 60 * 60);
    
    // Decay factor - providers used recently get slight preference
    return Math.max(0, 1 - (hoursSince / 24));
  }

  getProviderStats(): Map<string, any> {
    return new Map(this.providerStats);
  }
}

// Adaptive model wrapper that can switch providers on failure
export class AdaptiveModel extends BaseAIModel {
  private currentProvider?: string;
  private loadBalancer: ProviderLoadBalancer;

  constructor(
    info: ModelInfo,
    private multiProviderManager: MultiProviderManager,
    private requirements?: any
  ) {
    super(info);
    this.loadBalancer = new ProviderLoadBalancer(multiProviderManager);
  }

  protected async doInitialize(): Promise<void> {
    // Select optimal provider during initialization
    this.currentProvider = await this.loadBalancer.selectOptimalProvider(
      this.info.id, 
      this.requirements
    );
  }

  protected async checkReadiness(): Promise<boolean> {
    return !!this.currentProvider;
  }

  protected async doDispose(): Promise<void> {
    // Cleanup handled by MultiProviderManager
  }

  protected async doGenerateText(
    messages: Message[], 
    options: GenerationOptions
  ): Promise<GenerationResult> {
    return this.executeWithProviderFallback(async (model) => {
      return model.generateText(messages, options);
    });
  }

  protected async *doGenerateStream(
    messages: Message[], 
    options: GenerationOptions
  ) {
    const model = await this.getCurrentModel();
    yield* model.generateStream(messages, options);
  }

  protected async doGenerateStructured<T>(
    messages: Message[], 
    schema: any, 
    options: GenerationOptions
  ): Promise<{ data: T; result: GenerationResult }> {
    return this.executeWithProviderFallback(async (model) => {
      return model.generateStructured(messages, schema, options);
    });
  }

  private async executeWithProviderFallback<T>(
    operation: (model: AIModel) => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    let lastError: AIModelError | null = null;

    // Try current provider first
    if (this.currentProvider) {
      try {
        const model = await this.getCurrentModel();
        const result = await operation(model);
        
        this.loadBalancer.recordRequest(this.currentProvider, true, Date.now() - startTime);
        return result;
      } catch (error) {
        lastError = error instanceof AIModelError ? error : AIModelError.fromUnknown(error);
        this.loadBalancer.recordRequest(this.currentProvider, false, Date.now() - startTime);
        
        // If it's not retryable, don't try other providers
        if (!lastError.retryable) {
          throw lastError;
        }
      }
    }

    // Try other providers as fallback
    const allModels = await this.multiProviderManager.listAvailableModels();
    for (const [provider, models] of allModels) {
      if (provider === this.currentProvider) continue; // Already tried
      
      const hasModel = models.some(m => m.id === this.info.id);
      if (!hasModel) continue;

      try {
        const model = await this.multiProviderManager.createModel(this.info.id, provider);
        const result = await operation(model);
        
        // Switch to successful provider
        this.currentProvider = provider;
        this.loadBalancer.recordRequest(provider, true, Date.now() - startTime);
        return result;
      } catch (error) {
        const aiError = error instanceof AIModelError ? error : AIModelError.fromUnknown(error);
        this.loadBalancer.recordRequest(provider, false, Date.now() - startTime);
        lastError = aiError;
        continue;
      }
    }

    throw lastError || new AIModelError({
      type: 'MODEL_UNAVAILABLE_ERROR',
      message: 'All providers failed',
      retryable: false,
    });
  }

  private async getCurrentModel(): Promise<AIModel> {
    if (!this.currentProvider) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: 'No provider selected',
        retryable: false,
      });
    }

    return this.multiProviderManager.createModel(this.info.id, this.currentProvider);
  }
}

// Global multi-provider manager instance
export const multiProviderManager = new MultiProviderManager();