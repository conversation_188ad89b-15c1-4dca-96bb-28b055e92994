import { 
  UnifiedModelAdapter, 
  UnifiedModelAdapterConfig, 
  createUnifiedModelAdapter,
  globalAdapterManager 
} from './unified-adapter';
import { createAIProvider } from '../../services/ai-providers';
import { GenerationOptions, GenerationResult, Message } from './interfaces';

/**
 * 现有AI服务提供商与统一适配器的集成层
 * 
 * 这个类负责：
 * 1. 将现有的 ai-providers.ts 系统与新的统一适配器连接
 * 2. 提供向后兼容的API
 * 3. 管理适配器的生命周期
 */
export class AIProviderIntegration {
  private adapters: Map<string, UnifiedModelAdapter> = new Map();

  /**
   * 创建并注册统一适配器
   * @param provider 提供商名称 (如 'openai', 'claude', 'deepseek' 等)
   * @param apiKey API密钥
   * @param modelId 模型ID
   * @param options 额外配置选项
   */
  async createAdapter(
    provider: string,
    apiKey: string,
    modelId: string,
    options: {
      endpoint?: string;
      enableReasoning?: boolean;
      enableStructuredOutput?: boolean;
      reasoningConfig?: any;
    } = {}
  ): Promise<UnifiedModelAdapter> {
    const adapterKey = `${provider}:${modelId}`;
    
    // 检查是否已存在
    const existingAdapter = this.adapters.get(adapterKey);
    if (existingAdapter) {
      return existingAdapter;
    }

    // 创建统一适配器配置
    const config: UnifiedModelAdapterConfig = {
      modelId,
      provider,
      apiKey,
      endpoint: options.endpoint,
      enableReasoning: options.enableReasoning,
      enableStructuredOutput: options.enableStructuredOutput ?? true, // 默认启用结构化输出
      reasoningConfig: options.reasoningConfig,
      defaultOptions: {
        temperature: 0.7,
        maxTokens: 1000,
      }
    };

    // 创建适配器
    const adapter = createUnifiedModelAdapter(config);
    
    // 增强适配器以使用现有的 AI providers
    const enhancedAdapter = this.enhanceAdapterWithExistingProviders(adapter, provider, apiKey, modelId, options.endpoint);
    
    // 缓存和注册
    this.adapters.set(adapterKey, enhancedAdapter);
    globalAdapterManager.register(adapterKey, enhancedAdapter);
    
    return enhancedAdapter;
  }

  /**
   * 增强适配器以使用现有的 AI providers
   */
  private enhanceAdapterWithExistingProviders(
    adapter: UnifiedModelAdapter,
    provider: string,
    apiKey: string,
    modelId: string,
    endpoint?: string
  ): UnifiedModelAdapter {
    // 重写适配器的文本生成方法以使用现有的 AI providers
    const originalGenerateText = adapter.generateText.bind(adapter);
    
    adapter.generateText = async (messages: Message[], options: GenerationOptions = {}) => {
      try {
        // 使用现有的 AI provider 系统
        const aiProvider = await this.createAIProviderInstance(provider, apiKey, modelId, endpoint);
        
        // 将 Messages 转换为prompt格式（这里需要根据现有系统的格式进行调整）
        const prompt = this.messagesToPrompt(messages);
        
        // 调用现有的 generateContent 方法
        const aiResponse = await aiProvider.generateContent(prompt);
        
        // 使用适配器的推理处理能力
        const adapterInfo = adapter.getInfo();
        if (adapterInfo.isReasoningModel && adapterInfo.reasoningEnabled) {
          // 调用原始方法以获得推理处理
          return originalGenerateText(messages, options);
        }
        
        // 返回标准格式
        return {
          content: aiResponse.content || aiResponse,
          usage: aiResponse.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          metadata: {
            provider,
            modelId,
            isReasoningModel: adapterInfo.isReasoningModel
          }
        };
        
      } catch (error) {
        console.error(`Error in enhanced generateText for ${provider}:${modelId}:`, error);
        // 降级到原始方法
        return originalGenerateText(messages, options);
      }
    };
    
    return adapter;
  }

  /**
   * 创建现有 AI Provider 实例
   */
  private async createAIProviderInstance(provider: string, apiKey: string, modelId: string, endpoint?: string) {
    try {
      if (provider === 'ollama') {
        const ollamaEndpoint = endpoint || 'http://localhost:11434';
        return createAIProvider(provider, ollamaEndpoint, modelId, { endpoint: ollamaEndpoint });
      } else {
        return createAIProvider(provider, apiKey, modelId);
      }
    } catch (error) {
      console.error(`Failed to create AI provider for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * 将Messages转换为现有系统的prompt格式
   */
  private messagesToPrompt(messages: Message[]): string {
    // 简化的转换逻辑 - 可能需要根据具体需求调整
    return messages
      .filter(msg => msg.content && msg.content.trim().length > 0)
      .map(msg => {
        if (msg.role === 'system') {
          return `System: ${msg.content}`;
        } else if (msg.role === 'assistant') {
          return `Assistant: ${msg.content}`;
        } else {
          return msg.content; // user messages 直接使用内容
        }
      })
      .join('\n\n');
  }

  /**
   * 获取适配器
   */
  getAdapter(provider: string, modelId: string): UnifiedModelAdapter | undefined {
    const adapterKey = `${provider}:${modelId}`;
    return this.adapters.get(adapterKey);
  }

  /**
   * 移除适配器
   */
  removeAdapter(provider: string, modelId: string): boolean {
    const adapterKey = `${provider}:${modelId}`;
    globalAdapterManager.remove(adapterKey);
    return this.adapters.delete(adapterKey);
  }

  /**
   * 清理所有适配器
   */
  clearAll(): void {
    this.adapters.clear();
  }

  /**
   * 获取所有已注册的适配器
   */
  getAllAdapters(): Array<{
    provider: string;
    modelId: string;
    adapter: UnifiedModelAdapter;
    info: ReturnType<UnifiedModelAdapter['getInfo']>;
  }> {
    return Array.from(this.adapters.entries()).map(([key, adapter]) => {
      const [provider, modelId] = key.split(':');
      return {
        provider,
        modelId,
        adapter,
        info: adapter.getInfo()
      };
    });
  }

  /**
   * 批量健康检查
   */
  async healthCheckAll(): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    
    for (const [key, adapter] of this.adapters.entries()) {
      try {
        results[key] = await adapter.healthCheck();
      } catch (error) {
        results[key] = {
          status: 'unhealthy',
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
    
    return results;
  }
}

// 全局集成实例
export const globalAIProviderIntegration = new AIProviderIntegration();

/**
 * 便捷函数：基于现有配置创建推理感知的适配器
 */
export async function createReasoningAwareAdapter(
  provider: string,
  apiKey: string,
  modelId: string,
  endpoint?: string
): Promise<UnifiedModelAdapter> {
  return globalAIProviderIntegration.createAdapter(
    provider,
    apiKey,
    modelId,
    {
      endpoint,
      enableReasoning: true, // 启用推理功能
      enableStructuredOutput: true, // 启用结构化输出
    }
  );
}

/**
 * 便捷函数：获取或创建适配器
 */
export async function getOrCreateAdapter(
  provider: string,
  apiKey: string,
  modelId: string,
  endpoint?: string
): Promise<UnifiedModelAdapter> {
  // 先尝试获取现有适配器
  const existingAdapter = globalAIProviderIntegration.getAdapter(provider, modelId);
  if (existingAdapter) {
    return existingAdapter;
  }
  
  // 创建新适配器
  return createReasoningAwareAdapter(provider, apiKey, modelId, endpoint);
}