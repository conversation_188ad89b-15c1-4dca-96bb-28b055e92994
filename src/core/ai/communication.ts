import { AIModelError } from './interfaces';

// Message channel for cross-context communication
export interface MessageChannel {
  send<T = any>(message: T): Promise<void>;
  receive<T = any>(): AsyncIterable<T>;
  close(): Promise<void>;
}

// Port-based message channel for Chrome extensions
export class PortMessageChannel implements MessageChannel {
  private port: chrome.runtime.Port;
  private listeners = new Set<(message: any) => void>();
  private messageQueue: any[] = [];
  private closed = false;

  constructor(portName: string) {
    this.port = chrome.runtime.connect({ name: portName });
    this.port.onMessage.addListener(this.handleMessage.bind(this));
    this.port.onDisconnect.addListener(this.handleDisconnect.bind(this));
  }

  async send<T = any>(message: T): Promise<void> {
    if (this.closed) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: 'Message channel is closed',
        retryable: false,
      });
    }

    try {
      this.port.postMessage(message);
    } catch (error) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: `Failed to send message: ${error instanceof Error ? error.message : String(error)}`,
        retryable: true,
        originalError: error,
      });
    }
  }

  async *receive<T = any>(): AsyncIterable<T> {
    while (!this.closed) {
      // Yield queued messages first
      while (this.messageQueue.length > 0) {
        yield this.messageQueue.shift();
      }

      // Wait for new messages
      if (!this.closed) {
        const message = await this.waitForMessage();
        if (message !== null) {
          yield message;
        }
      }
    }
  }

  async close(): Promise<void> {
    if (!this.closed) {
      this.closed = true;
      this.port.disconnect();
      this.listeners.clear();
      this.messageQueue = [];
    }
  }

  private handleMessage(message: any): void {
    this.messageQueue.push(message);
    this.listeners.forEach(listener => listener(message));
  }

  private handleDisconnect(): void {
    this.closed = true;
  }

  private waitForMessage(): Promise<any | null> {
    return new Promise((resolve) => {
      if (this.messageQueue.length > 0) {
        resolve(this.messageQueue.shift());
        return;
      }

      if (this.closed) {
        resolve(null);
        return;
      }

      const listener = (message: any) => {
        this.listeners.delete(listener);
        resolve(message);
      };

      this.listeners.add(listener);

      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.listeners.has(listener)) {
          this.listeners.delete(listener);
          resolve(null);
        }
      }, 30000);
    });
  }
}

// Worker-based message channel
export class WorkerMessageChannel implements MessageChannel {
  private worker: Worker;
  private listeners = new Set<(message: any) => void>();
  private messageQueue: any[] = [];
  private closed = false;

  constructor(workerScript: string) {
    this.worker = new Worker(workerScript);
    this.worker.onmessage = this.handleMessage.bind(this);
    this.worker.onerror = this.handleError.bind(this);
  }

  async send<T = any>(message: T): Promise<void> {
    if (this.closed) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: 'Worker channel is closed',
        retryable: false,
      });
    }

    try {
      this.worker.postMessage(message);
    } catch (error) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: `Failed to send message to worker: ${error instanceof Error ? error.message : String(error)}`,
        retryable: true,
        originalError: error,
      });
    }
  }

  async *receive<T = any>(): AsyncIterable<T> {
    while (!this.closed) {
      while (this.messageQueue.length > 0) {
        yield this.messageQueue.shift();
      }

      if (!this.closed) {
        const message = await this.waitForMessage();
        if (message !== null) {
          yield message;
        }
      }
    }
  }

  async close(): Promise<void> {
    if (!this.closed) {
      this.closed = true;
      this.worker.terminate();
      this.listeners.clear();
      this.messageQueue = [];
    }
  }

  private handleMessage(event: MessageEvent): void {
    this.messageQueue.push(event.data);
    this.listeners.forEach(listener => listener(event.data));
  }

  private handleError(event: ErrorEvent): void {
    console.error('Worker error:', event.error);
    this.closed = true;
  }

  private waitForMessage(): Promise<any | null> {
    return new Promise((resolve) => {
      if (this.messageQueue.length > 0) {
        resolve(this.messageQueue.shift());
        return;
      }

      if (this.closed) {
        resolve(null);
        return;
      }

      const listener = (message: any) => {
        this.listeners.delete(listener);
        resolve(message);
      };

      this.listeners.add(listener);

      setTimeout(() => {
        if (this.listeners.has(listener)) {
          this.listeners.delete(listener);
          resolve(null);
        }
      }, 30000);
    });
  }
}

// Communication manager for handling different channel types
export class CommunicationManager {
  private channels = new Map<string, MessageChannel>();
  private requestHandlers = new Map<string, (request: any) => Promise<any>>();
  private responseHandlers = new Map<string, (response: any) => void>();

  createPortChannel(portName: string): MessageChannel {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: 'Chrome extension API not available',
        retryable: false,
      });
    }

    const channel = new PortMessageChannel(portName);
    this.channels.set(portName, channel);
    return channel;
  }

  createWorkerChannel(workerScript: string, channelId: string): MessageChannel {
    if (typeof Worker === 'undefined') {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: 'Web Workers not supported',
        retryable: false,
      });
    }

    const channel = new WorkerMessageChannel(workerScript);
    this.channels.set(channelId, channel);
    return channel;
  }

  getChannel(channelId: string): MessageChannel | undefined {
    return this.channels.get(channelId);
  }

  async closeChannel(channelId: string): Promise<void> {
    const channel = this.channels.get(channelId);
    if (channel) {
      await channel.close();
      this.channels.delete(channelId);
    }
  }

  async closeAllChannels(): Promise<void> {
    const closePromises = Array.from(this.channels.values()).map(channel => channel.close());
    await Promise.all(closePromises);
    this.channels.clear();
  }

  // Request-response pattern
  async sendRequest<TRequest, TResponse>(
    channelId: string,
    request: TRequest,
    timeoutMs: number = 30000
  ): Promise<TResponse> {
    const channel = this.getChannel(channelId);
    if (!channel) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: `Channel '${channelId}' not found`,
        retryable: false,
      });
    }

    const requestId = this.generateRequestId();
    const requestMessage = {
      type: 'request',
      id: requestId,
      data: request,
    };

    await channel.send(requestMessage);

    return new Promise<TResponse>((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.responseHandlers.delete(requestId);
        reject(new AIModelError({
          type: 'TIMEOUT_ERROR',
          message: `Request timeout after ${timeoutMs}ms`,
          retryable: true,
        }));
      }, timeoutMs);

      this.responseHandlers.set(requestId, (response: any) => {
        clearTimeout(timeout);
        this.responseHandlers.delete(requestId);
        
        if (response.error) {
          reject(new AIModelError({
            type: 'NETWORK_ERROR',
            message: response.error.message,
            retryable: response.error.retryable || false,
            originalError: response.error,
          }));
        } else {
          resolve(response.data);
        }
      });
    });
  }

  registerRequestHandler<TRequest, TResponse>(
    type: string,
    handler: (request: TRequest) => Promise<TResponse>
  ): void {
    this.requestHandlers.set(type, handler);
  }

  async startMessageListener(channelId: string): Promise<void> {
    const channel = this.getChannel(channelId);
    if (!channel) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: `Channel '${channelId}' not found`,
        retryable: false,
      });
    }

    try {
      for await (const message of channel.receive()) {
        await this.handleMessage(channel, message);
      }
    } catch (error) {
      console.error('Message listener error:', error);
    }
  }

  private async handleMessage(channel: MessageChannel, message: any): Promise<void> {
    try {
      if (message.type === 'request') {
        const handler = this.requestHandlers.get(message.data.type);
        if (handler) {
          try {
            const result = await handler(message.data);
            await channel.send({
              type: 'response',
              id: message.id,
              data: result,
            });
          } catch (error) {
            await channel.send({
              type: 'response',
              id: message.id,
              error: {
                message: error instanceof Error ? error.message : String(error),
                retryable: error instanceof AIModelError ? error.retryable : false,
              },
            });
          }
        }
      } else if (message.type === 'response') {
        const responseHandler = this.responseHandlers.get(message.id);
        if (responseHandler) {
          responseHandler(message);
        }
      }
    } catch (error) {
      console.error('Failed to handle message:', error);
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Timeout manager for handling operation timeouts
export class TimeoutManager {
  private timeouts = new Map<string, NodeJS.Timeout>();

  setTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    errorMessage: string = 'Operation timed out'
  ): Promise<T> {
    const timeoutId = this.generateTimeoutId();
    
    return Promise.race([
      promise.finally(() => this.clearTimeout(timeoutId)),
      new Promise<never>((_, reject) => {
        const timeout = setTimeout(() => {
          this.timeouts.delete(timeoutId);
          reject(new AIModelError({
            type: 'TIMEOUT_ERROR',
            message: errorMessage,
            retryable: true,
          }));
        }, timeoutMs);
        
        this.timeouts.set(timeoutId, timeout);
      }),
    ]);
  }

  clearTimeout(timeoutId: string): void {
    const timeout = this.timeouts.get(timeoutId);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(timeoutId);
    }
  }

  clearAllTimeouts(): void {
    for (const timeout of this.timeouts.values()) {
      clearTimeout(timeout);
    }
    this.timeouts.clear();
  }

  private generateTimeoutId(): string {
    return `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Global instances
export const communicationManager = new CommunicationManager();
export const timeoutManager = new TimeoutManager();