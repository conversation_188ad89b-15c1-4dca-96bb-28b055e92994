import { z } from 'zod';
import { ProviderConfig, GenerationOptions } from './interfaces';

// Environment detection schema
export const EnvironmentInfoSchema = z.object({
  memoryMB: z.number().min(0),
  platform: z.string(),
  userAgent: z.string().optional(),
  isExtension: z.boolean(),
  supportedFeatures: z.object({
    webStreams: z.boolean(),
    webWorkers: z.boolean(),
    indexedDB: z.boolean(),
    localStorage: z.boolean(),
  }),
});
export type EnvironmentInfo = z.infer<typeof EnvironmentInfoSchema>;

// Global configuration schema
export const AIConfigSchema = z.object({
  // Provider configurations
  providers: z.record(ProviderConfigSchema),
  
  // Default provider and model
  defaultProvider: z.string(),
  defaultModel: z.string(),
  
  // Global generation defaults
  defaultOptions: GenerationOptionsSchema.optional(),
  
  // Performance settings
  performance: z.object({
    enableStreaming: z.boolean().default(true),
    enableStructuredOutput: z.boolean().default(true),
    enableCaching: z.boolean().default(true),
    maxConcurrentRequests: z.number().min(1).max(10).default(3),
    requestTimeout: z.number().min(1000).default(30000),
  }),
  
  // Retry and fallback settings
  reliability: z.object({
    maxRetries: z.number().min(0).max(5).default(3),
    enableDegradation: z.boolean().default(true),
    fallbackProviders: z.array(z.string()).default([]),
  }),
  
  // Environment-specific optimizations
  environment: EnvironmentInfoSchema.optional(),
  
  // Feature flags
  features: z.object({
    enableAdvancedErrorHandling: z.boolean().default(true),
    enableMetrics: z.boolean().default(false),
    enableDebugLogging: z.boolean().default(false),
  }),
});
export type AIConfig = z.infer<typeof AIConfigSchema>;

// Provider configuration with validation
const ProviderConfigSchema = z.object({
  apiKey: z.string(),
  endpoint: z.string().url().optional(),
  model: z.string().optional(),
  defaultOptions: GenerationOptionsSchema.optional(),
  timeout: z.number().min(1000).optional(),
  retries: z.number().min(0).max(5).optional(),
  metadata: z.record(z.any()).optional(),
});

// Configuration manager
export class AIConfigManager {
  private config: AIConfig | null = null;
  private listeners: Array<(config: AIConfig) => void> = [];
  private storage: ConfigStorage;

  constructor(storage: ConfigStorage) {
    this.storage = storage;
  }

  async initialize(): Promise<void> {
    // Load configuration from storage
    const stored = await this.storage.load();
    
    // Merge with environment-based defaults
    const environment = await this.detectEnvironment();
    const defaults = this.generateDefaults(environment);
    
    this.config = AIConfigSchema.parse({
      ...defaults,
      ...stored,
      environment,
    });

    // Save back to storage to ensure consistency
    await this.storage.save(this.config);
  }

  getConfig(): AIConfig {
    if (!this.config) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }
    return this.config;
  }

  async updateConfig(updates: Partial<AIConfig>): Promise<void> {
    if (!this.config) {
      throw new Error('Configuration not initialized');
    }

    const newConfig = AIConfigSchema.parse({
      ...this.config,
      ...updates,
    });

    this.config = newConfig;
    await this.storage.save(newConfig);
    
    // Notify listeners
    this.listeners.forEach(listener => listener(newConfig));
  }

  async updateProvider(providerName: string, config: ProviderConfig): Promise<void> {
    await this.updateConfig({
      providers: {
        ...this.getConfig().providers,
        [providerName]: config,
      },
    });
  }

  getProviderConfig(providerName: string): ProviderConfig | undefined {
    return this.getConfig().providers[providerName];
  }

  onConfigChange(listener: (config: AIConfig) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private async detectEnvironment(): Promise<EnvironmentInfo> {
    // Detect memory (rough estimate based on navigator.deviceMemory)
    const memoryMB = typeof navigator !== 'undefined' && 'deviceMemory' in navigator
      ? (navigator as any).deviceMemory * 1024
      : 2048; // Default assumption

    // Detect platform
    const platform = typeof navigator !== 'undefined' 
      ? navigator.platform 
      : 'unknown';

    const userAgent = typeof navigator !== 'undefined' 
      ? navigator.userAgent 
      : undefined;

    // Check if running in extension context
    const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;

    // Check supported features
    const supportedFeatures = {
      webStreams: typeof ReadableStream !== 'undefined',
      webWorkers: typeof Worker !== 'undefined',
      indexedDB: typeof indexedDB !== 'undefined',
      localStorage: typeof localStorage !== 'undefined',
    };

    return {
      memoryMB,
      platform,
      userAgent,
      isExtension: Boolean(isExtension),
      supportedFeatures,
    };
  }

  private generateDefaults(environment: EnvironmentInfo): Partial<AIConfig> {
    // Adjust defaults based on environment
    const isLowMemory = environment.memoryMB < 4096;
    const isMobile = /Mobile|Android|iPhone|iPad/.test(environment.userAgent || '');

    return {
      providers: {},
      defaultProvider: 'openai',
      defaultModel: 'gpt-3.5-turbo',
      
      defaultOptions: {
        temperature: 0.7,
        maxTokens: isLowMemory ? 1000 : 2000,
        enableStreaming: environment.supportedFeatures.webStreams && !isMobile,
      },

      performance: {
        enableStreaming: environment.supportedFeatures.webStreams && !isMobile,
        enableStructuredOutput: true,
        enableCaching: environment.supportedFeatures.indexedDB,
        maxConcurrentRequests: isLowMemory ? 1 : 3,
        requestTimeout: isMobile ? 20000 : 30000,
      },

      reliability: {
        maxRetries: isLowMemory ? 1 : 3,
        enableDegradation: true,
        fallbackProviders: [],
      },

      features: {
        enableAdvancedErrorHandling: true,
        enableMetrics: !isLowMemory,
        enableDebugLogging: false,
      },
    };
  }
}

// Configuration storage interface
export interface ConfigStorage {
  load(): Promise<Partial<AIConfig>>;
  save(config: AIConfig): Promise<void>;
  clear(): Promise<void>;
}

// Chrome extension storage implementation
export class ChromeExtensionStorage implements ConfigStorage {
  private readonly key = 'ai_config';

  async load(): Promise<Partial<AIConfig>> {
    try {
      const result = await chrome.storage.sync.get([this.key]);
      return result[this.key] || {};
    } catch (error) {
      console.warn('Failed to load config from Chrome storage:', error);
      return {};
    }
  }

  async save(config: AIConfig): Promise<void> {
    try {
      await chrome.storage.sync.set({ [this.key]: config });
    } catch (error) {
      console.error('Failed to save config to Chrome storage:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      await chrome.storage.sync.remove([this.key]);
    } catch (error) {
      console.error('Failed to clear config from Chrome storage:', error);
      throw error;
    }
  }
}

// localStorage fallback implementation
export class LocalStorage implements ConfigStorage {
  private readonly key = 'ai_config';

  async load(): Promise<Partial<AIConfig>> {
    try {
      const stored = localStorage.getItem(this.key);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('Failed to load config from localStorage:', error);
      return {};
    }
  }

  async save(config: AIConfig): Promise<void> {
    try {
      localStorage.setItem(this.key, JSON.stringify(config));
    } catch (error) {
      console.error('Failed to save config to localStorage:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      localStorage.removeItem(this.key);
    } catch (error) {
      console.error('Failed to clear config from localStorage:', error);
      throw error;
    }
  }
}

// In-memory storage for testing
export class MemoryStorage implements ConfigStorage {
  private data: Partial<AIConfig> = {};

  async load(): Promise<Partial<AIConfig>> {
    return { ...this.data };
  }

  async save(config: AIConfig): Promise<void> {
    this.data = { ...config };
  }

  async clear(): Promise<void> {
    this.data = {};
  }
}

// Configuration factory
export function createConfigManager(storage?: ConfigStorage): AIConfigManager {
  if (storage) {
    return new AIConfigManager(storage);
  }

  // Auto-detect storage based on environment
  if (typeof chrome !== 'undefined' && chrome.storage) {
    return new AIConfigManager(new ChromeExtensionStorage());
  }

  if (typeof localStorage !== 'undefined') {
    return new AIConfigManager(new LocalStorage());
  }

  // Fallback to memory storage
  return new AIConfigManager(new MemoryStorage());
}