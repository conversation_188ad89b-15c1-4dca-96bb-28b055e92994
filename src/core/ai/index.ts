// Core AI architecture exports
export * from './interfaces';
export * from './factory';
export * from './base-model';
export * from './config';
export * from './error-handler';
export * from './streaming';
export * from './communication';
export * from './structured-output';
export * from './context';
export * from './multi-provider';
export * from './monitoring';
export * from './reasoning-middleware';
export * from './unified-adapter';
export * from './provider-integration';
export * from './reasoning-error-handler';
export * from './enhanced-reasoning-parser';
export * from './middleware-pipeline';
export * from './error-recovery';

// Re-export commonly used types for convenience
export type {
  Message,
  MessageRole,
  TokenUsage,
  ModelCapabilities,
  ModelInfo,
  GenerationOptions,
  GenerationResult,
  ReasoningResult,
  StreamChunk,
  ProviderConfig,
  ContextWindow,
  AIErrorType,
  AIError,
  AIConfig,
  EnvironmentInfo,
  ErrorStats,
} from './interfaces';

// Enhanced reasoning parser exports
export type {
  ReasoningParseConfig,
  EnhancedReasoningResult,
  CleanupRule,
} from './enhanced-reasoning-parser';

export {
  EnhancedReasoningParser,
  ReasoningModelType,
  parseReasoningContent,
} from './enhanced-reasoning-parser';

// Middleware pipeline exports
export type {
  MiddlewareContext,
  MiddlewareResult,
  ResponseMiddlewareContext,
  ResponseMiddlewareResult,
  RequestMiddleware,
  ResponseMiddleware,
  MiddlewareConfig,
} from './middleware-pipeline';

export {
  ReasoningControlMiddleware,
  RequestValidationMiddleware,
  ContextLengthMiddleware,
  ReasoningParsingMiddleware,
  StructuredOutputMiddleware,
  MiddlewarePipeline,
  createDefaultPipeline,
  defaultPipeline,
} from './middleware-pipeline';

// Error recovery exports
export type {
  RetryConfig,
  FallbackConfig,
  CircuitBreakerConfig,
  ErrorRecoveryContext,
  RecoveryResult,
} from './error-recovery';

export {
  SmartRetryHandler,
  FallbackHandler,
  CircuitBreaker,
  ErrorRecoveryManager,
  createDefaultRecoveryManager,
  globalRecoveryManager,
} from './error-recovery';

// Re-export core classes
export {
  AIModelError,
  AIProviderRegistry,
  AIOperationManager,
  BaseAIModel,
  withRetry,
  defaultRetryOptions,
  aiProviderRegistry,
} from './factory';

export {
  AIConfigManager,
  ChromeExtensionStorage,
  LocalStorage,
  MemoryStorage,
  createConfigManager,
} from './config';

export {
  AIErrorHandler,
  ErrorClassifier,
  ConsoleErrorReporter,
  globalErrorHandler,
} from './error-handler';

export {
  StreamManager,
  StreamBuffer,
  StreamingUtils,
  streamManager,
} from './streaming';

export {
  CommunicationManager,
  TimeoutManager,
  PortMessageChannel,
  WorkerMessageChannel,
  communicationManager,
  timeoutManager,
} from './communication';

export {
  StructuredOutputManager,
  NativeStructuredOutputStrategy,
  TextWithValidationStrategy,
  PromptGuidedStrategy,
  structuredOutputManager,
} from './structured-output';

export {
  ReasoningModelWrapper,
  ReasoningContentParser,
  createReasoningWrapper,
  globalReasoningProcessor,
} from './reasoning-middleware';

export {
  UnifiedModelAdapter,
  createUnifiedModelAdapter,
  AdapterManager,
  globalAdapterManager,
} from './unified-adapter';

export {
  AIProviderIntegration,
  globalAIProviderIntegration,
  createReasoningAwareAdapter,
  getOrCreateAdapter,
} from './provider-integration';

export {
  ReasoningErrorHandler,
  RecoveryStrategy,
  globalReasoningErrorHandler,
} from './reasoning-error-handler';

export {
  ContextManager,
  ContextWindowManager,
  SimpleTokenCounter,
  KeepRecentStrategy,
  SummarizeOldStrategy,
  contextManager,
} from './context';

export {
  MultiProviderManager,
  ProviderLoadBalancer,
  AdaptiveModel,
  multiProviderManager,
} from './multi-provider';

export {
  PerformanceMonitor,
  ResourceMonitor,
  HealthChecker,
  MemoryMetricsCollector,
  performanceMonitor,
  resourceMonitor,
  healthChecker,
} from './monitoring';

// Version information
export const AI_CORE_VERSION = '2.0.0';