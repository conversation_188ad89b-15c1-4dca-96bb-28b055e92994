import { z } from 'zod';
import { 
  AIModel, 
  Message, 
  GenerationOptions, 
  GenerationResult, 
  StreamChunk, 
  ModelInfo,
  TokenUsage,
  AIModelError
} from './interfaces';

// Base implementation with common functionality
export abstract class BaseAIModel implements AIModel {
  public readonly info: ModelInfo;
  protected initialized = false;
  protected disposed = false;

  constructor(info: ModelInfo) {
    this.info = info;
  }

  // Lifecycle methods
  async initialize(): Promise<void> {
    if (this.initialized) return;
    await this.doInitialize();
    this.initialized = true;
  }

  async isReady(): Promise<boolean> {
    return this.initialized && !this.disposed && await this.checkReadiness();
  }

  async dispose(): Promise<void> {
    if (this.disposed) return;
    await this.doDispose();
    this.disposed = true;
    this.initialized = false;
  }

  // Generation methods with common error handling
  async generateText(
    messages: Message[], 
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    await this.ensureReady();
    this.validateMessages(messages);
    
    try {
      return await this.doGenerateText(messages, options);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async *generateStream(
    messages: Message[], 
    options: GenerationOptions = {}
  ): AsyncIterable<StreamChunk> {
    await this.ensureReady();
    this.validateMessages(messages);

    if (!this.info.capabilities.supportsStreaming) {
      throw new AIModelError({
        type: 'MODEL_UNAVAILABLE_ERROR',
        message: `Model ${this.info.id} does not support streaming`,
        retryable: false,
        fallbackOptions: ['disable-streaming'],
      });
    }

    try {
      yield* this.doGenerateStream(messages, options);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateStructured<T>(
    messages: Message[], 
    schema: z.ZodSchema<T>, 
    options: GenerationOptions = {}
  ): Promise<{ data: T; result: GenerationResult }> {
    await this.ensureReady();
    this.validateMessages(messages);

    // Try native structured output first
    if (this.info.capabilities.supportsStructuredOutput && options.enableStructuredOutput !== false) {
      try {
        return await this.doGenerateStructured(messages, schema, options);
      } catch (error) {
        console.warn('Native structured output failed, falling back to text generation');
      }
    }

    // Fallback to text generation + validation
    const result = await this.generateText(messages, {
      ...options,
      enableStructuredOutput: false,
    });

    try {
      const parsedData = this.parseStructuredContent(result.content, schema);
      return { data: parsedData, result };
    } catch (error) {
      throw new AIModelError({
        type: 'PARSING_ERROR',
        message: `Failed to parse structured output: ${error instanceof Error ? error.message : String(error)}`,
        retryable: true,
        fallbackOptions: ['disable-structured-output'],
        originalError: error,
      });
    }
  }

  // Abstract methods to be implemented by concrete models
  protected abstract doInitialize(): Promise<void>;
  protected abstract checkReadiness(): Promise<boolean>;
  protected abstract doDispose(): Promise<void>;
  protected abstract doGenerateText(messages: Message[], options: GenerationOptions): Promise<GenerationResult>;
  protected abstract doGenerateStream(messages: Message[], options: GenerationOptions): AsyncIterable<StreamChunk>;
  protected abstract doGenerateStructured<T>(
    messages: Message[], 
    schema: z.ZodSchema<T>, 
    options: GenerationOptions
  ): Promise<{ data: T; result: GenerationResult }>;

  // Helper methods
  protected async ensureReady(): Promise<void> {
    if (this.disposed) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: 'Model has been disposed',
        retryable: false,
      });
    }

    if (!this.initialized) {
      await this.initialize();
    }

    if (!await this.isReady()) {
      throw new AIModelError({
        type: 'MODEL_UNAVAILABLE_ERROR',
        message: 'Model is not ready',
        retryable: true,
      });
    }
  }

  protected validateMessages(messages: Message[]): void {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new AIModelError({
        type: 'VALIDATION_ERROR',
        message: 'Messages array cannot be empty',
        retryable: false,
      });
    }

    for (const message of messages) {
      try {
        Message.parse(message);
      } catch (error) {
        throw new AIModelError({
          type: 'VALIDATION_ERROR',
          message: `Invalid message format: ${error instanceof Error ? error.message : String(error)}`,
          retryable: false,
          originalError: error,
        });
      }
    }
  }

  protected parseStructuredContent<T>(content: string, schema: z.ZodSchema<T>): T {
    // Try to extract JSON from content
    let jsonStr = content.trim();

    // Remove markdown code blocks
    jsonStr = jsonStr.replace(/^```(?:json)?\s*/, '').replace(/```\s*$/, '');

    // Remove comments
    jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

    // Try to find JSON object in the string
    const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    }

    // Clean up trailing commas
    jsonStr = jsonStr.replace(/,\s*}/g, '}').replace(/,\s*]/g, ']');

    try {
      const parsedData = JSON.parse(jsonStr);
      return schema.parse(parsedData);
    } catch (error) {
      throw new Error(`Failed to parse JSON: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  protected handleError(error: unknown): AIModelError {
    if (error instanceof AIModelError) {
      return error;
    }

    if (error instanceof Error) {
      // Map common error patterns
      if (error.message.includes('timeout')) {
        return new AIModelError({
          type: 'TIMEOUT_ERROR',
          message: error.message,
          retryable: true,
          originalError: error,
        });
      }

      if (error.message.includes('rate limit') || error.message.includes('429')) {
        return new AIModelError({
          type: 'RATE_LIMIT_ERROR',
          message: error.message,
          retryable: true,
          originalError: error,
        });
      }

      if (error.message.includes('auth') || error.message.includes('401')) {
        return new AIModelError({
          type: 'AUTHENTICATION_ERROR',
          message: error.message,
          retryable: false,
          originalError: error,
        });
      }

      if (error.message.includes('context') || error.message.includes('token')) {
        return new AIModelError({
          type: 'CONTEXT_LENGTH_ERROR',
          message: error.message,
          retryable: true,
          fallbackOptions: ['reduce-context'],
          originalError: error,
        });
      }

      if (error.message.includes('network') || error.message.includes('fetch')) {
        return new AIModelError({
          type: 'NETWORK_ERROR',
          message: error.message,
          retryable: true,
          originalError: error,
        });
      }
    }

    return AIModelError.fromUnknown(error);
  }

  protected createTokenUsage(promptTokens: number, completionTokens: number): TokenUsage {
    return {
      promptTokens,
      completionTokens,
      totalTokens: promptTokens + completionTokens,
    };
  }

  protected formatMessages(messages: Message[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
    }));
  }

  protected addTimestamp(messages: Message[]): Message[] {
    const timestamp = Date.now();
    return messages.map(msg => ({
      ...msg,
      timestamp: msg.timestamp || timestamp,
    }));
  }

  // Utility method for timeout handling
  protected withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Operation timed out')), timeoutMs)
      ),
    ]);
  }
}