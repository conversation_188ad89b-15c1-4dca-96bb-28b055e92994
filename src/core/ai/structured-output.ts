import { z } from 'zod';
import { AIModelError, Message, GenerationOptions, GenerationResult, ReasoningResult } from './interfaces';
import { ReasoningContentParser, ReasoningMiddlewareConfig } from './reasoning-middleware';

// Schema validation result
export interface SchemaValidationResult<T> {
  success: boolean;
  data?: T;
  errors?: z.ZodError;
}

// Structured output strategy interface
export interface StructuredOutputStrategy {
  name: string;
  canHandle(schema: z.ZodSchema): boolean;
  generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options?: GenerationOptions
  ): Promise<{ data: T; result: GenerationResult }>;
}

// Enhanced structured output strategy interface with reasoning support
export interface ReasoningAwareStructuredOutputStrategy extends StructuredOutputStrategy {
  generateStructuredWithReasoning<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options?: GenerationOptions,
    reasoningConfig?: ReasoningMiddlewareConfig
  ): Promise<{ data: T; result: GenerationResult; reasoning?: string }>;
}

// Native structured output strategy (for providers that support it)
export class NativeStructuredOutputStrategy implements StructuredOutputStrategy {
  name = 'native';

  canHandle(schema: z.ZodSchema): boolean {
    // Most providers support basic object schemas
    return schema instanceof z.ZodObject || 
           schema instanceof z.ZodArray ||
           schema instanceof z.ZodUnion;
  }

  async generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {}
  ): Promise<{ data: T; result: GenerationResult }> {
    // Convert Zod schema to JSON Schema for provider
    const jsonSchema = this.zodToJsonSchema(schema);
    
    const result = await generator(messages, {
      ...options,
      enableStructuredOutput: true,
      responseSchema: jsonSchema,
    });

    // Parse and validate the result
    const parseResult = this.parseAndValidate(result.content, schema);
    if (!parseResult.success) {
      throw new AIModelError({
        type: 'PARSING_ERROR',
        message: `Native structured output validation failed: ${parseResult.errors?.message}`,
        retryable: true,
        fallbackOptions: ['text-with-validation', 'prompt-guided'],
        originalError: parseResult.errors,
      });
    }

    return { data: parseResult.data!, result };
  }

  private zodToJsonSchema(schema: z.ZodSchema): any {
    // Simplified Zod to JSON Schema conversion
    // In production, you might want to use a library like zod-to-json-schema
    if (schema instanceof z.ZodObject) {
      const properties: any = {};
      const shape = (schema as any).shape;
      
      for (const [key, value] of Object.entries(shape)) {
        properties[key] = this.zodTypeToJsonSchema(value as z.ZodSchema);
      }

      return {
        type: 'object',
        properties,
        required: Object.keys(shape),
      };
    }

    if (schema instanceof z.ZodArray) {
      return {
        type: 'array',
        items: this.zodTypeToJsonSchema(schema.element),
      };
    }

    return this.zodTypeToJsonSchema(schema);
  }

  private zodTypeToJsonSchema(schema: z.ZodSchema): any {
    if (schema instanceof z.ZodString) return { type: 'string' };
    if (schema instanceof z.ZodNumber) return { type: 'number' };
    if (schema instanceof z.ZodBoolean) return { type: 'boolean' };
    if (schema instanceof z.ZodArray) return {
      type: 'array',
      items: this.zodTypeToJsonSchema(schema.element)
    };
    if (schema instanceof z.ZodObject) {
      const shape = (schema as any).shape;
      const properties: any = {};
      for (const [key, value] of Object.entries(shape)) {
        properties[key] = this.zodTypeToJsonSchema(value as z.ZodSchema);
      }
      return {
        type: 'object',
        properties,
        required: Object.keys(shape)
      };
    }
    return { type: 'string' }; // fallback
  }

  private parseAndValidate<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    try {
      const parsed = JSON.parse(content);
      const validated = schema.parse(parsed);
      return { success: true, data: validated };
    } catch (error) {
      return { 
        success: false, 
        errors: error instanceof z.ZodError ? error : new z.ZodError([])
      };
    }
  }
}

// Text generation with validation strategy (fallback)
export class TextWithValidationStrategy implements ReasoningAwareStructuredOutputStrategy {
  name = 'text-with-validation';

  canHandle(schema: z.ZodSchema): boolean {
    return true; // Can handle any schema as fallback
  }

  async generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {}
  ): Promise<{ data: T; result: GenerationResult }> {
    const result = await this.generateStructuredWithReasoning(
      messages, 
      schema, 
      generator, 
      options
    );
    
    return { data: result.data, result: result.result };
  }

  async generateStructuredWithReasoning<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {},
    reasoningConfig?: ReasoningMiddlewareConfig
  ): Promise<{ data: T; result: GenerationResult; reasoning?: string }> {
    const result = await generator(messages, {
      ...options,
      enableStructuredOutput: false, // Disable native structured output
    });

    // If reasoning is enabled, parse the content to extract reasoning and text
    let contentToParse = result.content;
    let reasoning: string | undefined;

    if (reasoningConfig?.enabled) {
      const reasoningParser = new ReasoningContentParser(reasoningConfig);
      const parsed = reasoningParser.parseReasoningContent(result.content);
      
      if (reasoningParser.validateParsingResult(parsed)) {
        reasoning = parsed.reasoning;
        contentToParse = reasoningParser.cleanFinalText(parsed.text);
      }
    }

    // Try multiple parsing approaches
    const parseResult = this.tryParseContent(contentToParse, schema);
    if (!parseResult.success) {
      throw new AIModelError({
        type: 'PARSING_ERROR',
        message: `Failed to parse structured output: ${parseResult.errors?.message}`,
        retryable: true,
        fallbackOptions: ['prompt-guided'],
        originalError: parseResult.errors,
      });
    }

    return { 
      data: parseResult.data!, 
      result: { ...result, content: contentToParse },
      reasoning 
    };
  }

  private tryParseContent<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    const parsers = [
      () => this.parseDirectJson(content, schema),
      () => this.parseJsonFromMarkdown(content, schema),
      () => this.parseJsonFromText(content, schema),
      () => this.parsePartialJson(content, schema),
      // Enhanced parsers for reasoning models
      () => this.parseJsonFromThinkTags(content, schema),
      () => this.parseJsonAfterReasoning(content, schema),
    ];

    for (const parser of parsers) {
      try {
        const result = parser();
        if (result.success) return result;
      } catch (error) {
        // Continue to next parser
        continue;
      }
    }

    return { success: false, errors: new z.ZodError([]) };
  }

  private parseDirectJson<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    const parsed = JSON.parse(content);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }

  private parseJsonFromMarkdown<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    // Extract JSON from markdown code blocks
    const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (!jsonMatch) throw new Error('No JSON block found');
    
    const parsed = JSON.parse(jsonMatch[1]);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }

  private parseJsonFromText<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    // Find JSON-like objects in text
    const jsonMatch = content.match(/\{[\s\S]*?\}/);
    if (!jsonMatch) throw new Error('No JSON found in text');
    
    const parsed = JSON.parse(jsonMatch[0]);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }

  private parsePartialJson<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    // Try to fix common JSON issues
    let cleanContent = content
      .replace(/,\s*}/g, '}')  // Remove trailing commas
      .replace(/,\s*]/g, ']')
      .replace(/'/g, '"')      // Replace single quotes
      .replace(/(\w+):/g, '"$1":'); // Quote unquoted keys

    const parsed = JSON.parse(cleanContent);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }

  // Enhanced parsers for reasoning models
  private parseJsonFromThinkTags<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    // Extract content outside of <think> tags
    const thinkTagRegex = /<think>[\s\S]*?<\/think>/gi;
    const contentWithoutThinking = content.replace(thinkTagRegex, '').trim();
    
    if (!contentWithoutThinking) throw new Error('No content found outside think tags');
    
    // Try to find JSON in the remaining content
    const jsonMatch = contentWithoutThinking.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
    if (!jsonMatch) throw new Error('No JSON found outside think tags');
    
    const parsed = JSON.parse(jsonMatch[0]);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }

  private parseJsonAfterReasoning<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    // Look for JSON after reasoning indicators
    const reasoningIndicators = [
      'reasoning:',
      'analysis:',
      'thinking:',
      'conclusion:',
      'answer:',
      'result:',
      'output:'
    ];
    
    let bestMatch: string | null = null;
    let bestPosition = content.length;
    
    // Find the last reasoning indicator
    for (const indicator of reasoningIndicators) {
      const index = content.toLowerCase().lastIndexOf(indicator.toLowerCase());
      if (index !== -1 && index < bestPosition) {
        bestPosition = index;
      }
    }
    
    if (bestPosition < content.length) {
      const afterReasoning = content.substring(bestPosition);
      const jsonMatch = afterReasoning.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
      if (jsonMatch) {
        bestMatch = jsonMatch[0];
      }
    }
    
    // Fallback: look for the last JSON in the entire content
    if (!bestMatch) {
      const matches = Array.from(content.matchAll(/\{[\s\S]*?\}|\[[\s\S]*?\]/g));
      if (matches.length > 0) {
        bestMatch = matches[matches.length - 1][0];
      }
    }
    
    if (!bestMatch) throw new Error('No JSON found after reasoning');
    
    const parsed = JSON.parse(bestMatch);
    const validated = schema.parse(parsed);
    return { success: true, data: validated };
  }
}

// Prompt-guided strategy (with explicit instructions)
export class PromptGuidedStrategy implements ReasoningAwareStructuredOutputStrategy {
  name = 'prompt-guided';

  canHandle(schema: z.ZodSchema): boolean {
    return true;
  }

  async generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {}
  ): Promise<{ data: T; result: GenerationResult }> {
    const result = await this.generateStructuredWithReasoning(
      messages, 
      schema, 
      generator, 
      options
    );
    
    return { data: result.data, result: result.result };
  }

  async generateStructuredWithReasoning<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {},
    reasoningConfig?: ReasoningMiddlewareConfig
  ): Promise<{ data: T; result: GenerationResult; reasoning?: string }> {
    // Create enhanced prompt with schema guidance
    const enhancedMessages = this.addSchemaGuidance(messages, schema, reasoningConfig);
    
    const result = await generator(enhancedMessages, {
      ...options,
      enableStructuredOutput: false,
      temperature: Math.min(options.temperature || 0.7, 0.3), // Lower temperature for consistency
    });

    // If reasoning is enabled, parse the content to extract reasoning and text
    let contentToParse = result.content;
    let reasoning: string | undefined;

    if (reasoningConfig?.enabled) {
      const reasoningParser = new ReasoningContentParser(reasoningConfig);
      const parsed = reasoningParser.parseReasoningContent(result.content);
      
      if (reasoningParser.validateParsingResult(parsed)) {
        reasoning = parsed.reasoning;
        contentToParse = reasoningParser.cleanFinalText(parsed.text);
      }
    }

    // Parse and validate
    const parseResult = this.parseWithGuidance(contentToParse, schema);
    if (!parseResult.success) {
      throw new AIModelError({
        type: 'PARSING_ERROR',
        message: `Prompt-guided parsing failed: ${parseResult.errors?.message}`,
        retryable: false, // Last fallback
        originalError: parseResult.errors,
      });
    }

    return { 
      data: parseResult.data!, 
      result: { ...result, content: contentToParse },
      reasoning 
    };
  }

  private addSchemaGuidance(messages: Message[], schema: z.ZodSchema, reasoningConfig?: ReasoningMiddlewareConfig): Message[] {
    const schemaDescription = this.generateSchemaDescription(schema);
    
    let guidanceContent = `You must respond with valid JSON that matches this exact schema:

${schemaDescription}

Important rules:
- Respond ONLY with valid JSON, no additional text
- Use double quotes for strings
- Include all required fields
- Follow the exact structure specified
- Do not include markdown formatting or code blocks

Example format: {"field1": "value", "field2": 123}`;

    // Add reasoning-specific guidance if enabled
    if (reasoningConfig?.enabled) {
      const tagName = reasoningConfig.tagName || 'think';
      guidanceContent = `You can use <${tagName}> tags for your reasoning process, but you must still provide the final JSON response outside the tags.

${guidanceContent}

If you need to think through the problem, use <${tagName}>your reasoning here</${tagName}> and then provide your JSON response.`;
    }

    const guidanceMessage: Message = {
      role: 'system',
      content: guidanceContent,
      timestamp: Date.now(),
    };

    // Insert guidance before the last user message
    const result = [...messages];
    result.splice(-1, 0, guidanceMessage);
    
    return result;
  }

  public generateSchemaDescription(schema: z.ZodSchema): string {
    // Generate human-readable schema description
    if (schema instanceof z.ZodObject) {
      const shape = (schema as any).shape;
      const fields = Object.entries(shape).map(([key, value]) => {
        const type = this.getZodTypeName(value as z.ZodSchema);
        return `  "${key}": ${type}`;
      });
      
      return `{\n${fields.join(',\n')}\n}`;
    }

    if (schema instanceof z.ZodArray) {
      const itemType = this.getZodTypeName(schema.element);
      return `[${itemType}, ...]`;
    }

    return this.getZodTypeName(schema);
  }

  private getZodTypeName(schema: z.ZodSchema): string {
    if (schema instanceof z.ZodString) return 'string';
    if (schema instanceof z.ZodNumber) return 'number';
    if (schema instanceof z.ZodBoolean) return 'boolean';
    if (schema instanceof z.ZodArray) return `array of ${this.getZodTypeName(schema.element)}`;
    if (schema instanceof z.ZodObject) return 'object';
    return 'any';
  }

  private parseWithGuidance<T>(content: string, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    try {
      // Clean up the content
      let cleanContent = content.trim();
      
      // Remove any non-JSON text before/after
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }

      const parsed = JSON.parse(cleanContent);
      const validated = schema.parse(parsed);
      return { success: true, data: validated };
    } catch (error) {
      return { 
        success: false, 
        errors: error instanceof z.ZodError ? error : new z.ZodError([])
      };
    }
  }
}

// Structured output manager
export class StructuredOutputManager {
  private strategies: StructuredOutputStrategy[] = [
    new NativeStructuredOutputStrategy(),
    new TextWithValidationStrategy(),
    new PromptGuidedStrategy(),
  ];

  addStrategy(strategy: StructuredOutputStrategy): void {
    this.strategies.push(strategy);
  }

  async generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {}
  ): Promise<{ data: T; result: GenerationResult }> {
    let lastError: AIModelError | null = null;

    for (const strategy of this.strategies) {
      if (!strategy.canHandle(schema)) continue;

      try {
        console.debug(`Trying structured output strategy: ${strategy.name}`);
        const result = await strategy.generateStructured(messages, schema, generator, options);
        console.debug(`Strategy ${strategy.name} succeeded`);
        return result;
      } catch (error) {
        console.warn(`Strategy ${strategy.name} failed:`, error);
        
        if (error instanceof AIModelError) {
          lastError = error;
          
          // If this strategy suggests fallbacks, continue to next strategy
          if (error.fallbackOptions && error.fallbackOptions.length > 0) {
            continue;
          }
        }

        // If it's not an AIModelError or no fallbacks suggested, try next strategy anyway
        lastError = AIModelError.fromUnknown(error, 'PARSING_ERROR');
      }
    }

    // All strategies failed
    throw lastError || new AIModelError({
      type: 'PARSING_ERROR',
      message: 'All structured output strategies failed',
      retryable: false,
    });
  }

  async generateStructuredWithReasoning<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    generator: (messages: Message[], options: GenerationOptions) => Promise<GenerationResult>,
    options: GenerationOptions = {},
    reasoningConfig?: ReasoningMiddlewareConfig
  ): Promise<{ data: T; result: GenerationResult; reasoning?: string }> {
    let lastError: AIModelError | null = null;

    // Filter strategies that support reasoning
    const reasoningStrategies = this.strategies.filter(strategy => 
      'generateStructuredWithReasoning' in strategy
    ) as ReasoningAwareStructuredOutputStrategy[];

    for (const strategy of reasoningStrategies) {
      if (!strategy.canHandle(schema)) continue;

      try {
        console.debug(`Trying reasoning-aware structured output strategy: ${strategy.name}`);
        const result = await strategy.generateStructuredWithReasoning(
          messages, 
          schema, 
          generator, 
          options, 
          reasoningConfig
        );
        console.debug(`Reasoning strategy ${strategy.name} succeeded`);
        return result;
      } catch (error) {
        console.warn(`Reasoning strategy ${strategy.name} failed:`, error);
        
        if (error instanceof AIModelError) {
          lastError = error;
          
          // If this strategy suggests fallbacks, continue to next strategy
          if (error.fallbackOptions && error.fallbackOptions.length > 0) {
            continue;
          }
        }

        // If it's not an AIModelError or no fallbacks suggested, try next strategy anyway
        lastError = AIModelError.fromUnknown(error, 'PARSING_ERROR');
      }
    }

    // If no reasoning-aware strategies worked, fall back to regular structured output
    console.debug('Falling back to regular structured output');
    const regularResult = await this.generateStructured(messages, schema, generator, options);
    return { ...regularResult, reasoning: undefined };
  }

  validateSchema<T>(data: unknown, schema: z.ZodSchema<T>): SchemaValidationResult<T> {
    try {
      const validated = schema.parse(data);
      return { success: true, data: validated };
    } catch (error) {
      return { 
        success: false, 
        errors: error instanceof z.ZodError ? error : new z.ZodError([])
      };
    }
  }

  describeSchema(schema: z.ZodSchema): string {
    return new PromptGuidedStrategy().generateSchemaDescription(schema);
  }
}

// Global structured output manager
export const structuredOutputManager = new StructuredOutputManager();