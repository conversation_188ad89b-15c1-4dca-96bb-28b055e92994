import { z } from 'zod';
import { 
  AIProviderFactory, 
  AIModel, 
  ModelInfo, 
  ProviderConfig,
  AIModelError,
  AIErrorType,
  ContextManager,
} from './interfaces';

// Registry for provider factories
export class AIProviderRegistry {
  private static instance: AIProviderRegistry;
  private providers = new Map<string, AIProviderFactory>();

  private constructor() {}

  static getInstance(): AIProviderRegistry {
    if (!AIProviderRegistry.instance) {
      AIProviderRegistry.instance = new AIProviderRegistry();
    }
    return AIProviderRegistry.instance;
  }

  register(factory: AIProviderFactory): void {
    this.providers.set(factory.name.toLowerCase(), factory);
  }

  unregister(providerName: string): void {
    this.providers.delete(providerName.toLowerCase());
  }

  getProvider(providerName: string): AIProviderFactory | undefined {
    return this.providers.get(providerName.toLowerCase());
  }

  listProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  async createModel(providerName: string, modelId: string, config: ProviderConfig): Promise<AIModel> {
    const provider = this.getProvider(providerName);
    if (!provider) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: `Provider '${providerName}' not found`,
        retryable: false,
      });
    }

    // Validate configuration
    const isValidConfig = await provider.validateConfig(config);
    if (!isValidConfig) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: `Invalid configuration for provider '${providerName}'`,
        retryable: false,
      });
    }

    return provider.createModel(modelId, config);
  }

  async listAvailableModels(providerName: string, config: ProviderConfig): Promise<ModelInfo[]> {
    const provider = this.getProvider(providerName);
    if (!provider) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: `Provider '${providerName}' not found`,
        retryable: false,
      });
    }

    return provider.listAvailableModels(config);
  }
}

// Retry mechanism for AI operations
export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: AIErrorType[];
}

export const defaultRetryOptions: RetryOptions = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryableErrors: [
    'RATE_LIMIT_ERROR',
    'NETWORK_ERROR',
    'TIMEOUT_ERROR',
  ],
};

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const opts = { ...defaultRetryOptions, ...options };
  let lastError: AIModelError | null = null;

  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (!(error instanceof AIModelError)) {
        throw AIModelError.fromUnknown(error);
      }

      lastError = error;

      // Don't retry if error is not retryable or we've exhausted attempts
      if (!error.retryable || !opts.retryableErrors.includes(error.type) || attempt === opts.maxAttempts) {
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        opts.baseDelay * Math.pow(opts.backoffFactor, attempt - 1),
        opts.maxDelay
      );

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// Degradation strategies
export interface DegradationStrategy {
  name: string;
  canApply(error: AIModelError, options: any): boolean;
  apply(options: any): any;
}

export class StreamingDegradationStrategy implements DegradationStrategy {
  name = 'disable-streaming';

  canApply(error: AIModelError, options: any): boolean {
    return options.enableStreaming === true && (
      error.type === 'MODEL_UNAVAILABLE_ERROR' ||
      error.type === 'NETWORK_ERROR'
    );
  }

  apply(options: any): any {
    return { ...options, enableStreaming: false };
  }
}

export class StructuredOutputDegradationStrategy implements DegradationStrategy {
  name = 'disable-structured-output';

  canApply(error: AIModelError, options: any): boolean {
    return options.enableStructuredOutput === true && (
      error.type === 'MODEL_UNAVAILABLE_ERROR' ||
      error.type === 'PARSING_ERROR'
    );
  }

  apply(options: any): any {
    return { ...options, enableStructuredOutput: false, responseSchema: undefined };
  }
}

export class ContextLengthDegradationStrategy implements DegradationStrategy {
  name = 'reduce-context';

  canApply(error: AIModelError, options: any): boolean {
    return error.type === 'CONTEXT_LENGTH_ERROR';
  }

  apply(options: any): any {
    const currentMaxTokens = options.maxTokens || 1000;
    return { ...options, maxTokens: Math.floor(currentMaxTokens * 0.7) };
  }
}

export class AIOperationManager {
  private degradationStrategies: DegradationStrategy[] = [
    new StreamingDegradationStrategy(),
    new StructuredOutputDegradationStrategy(), 
    new ContextLengthDegradationStrategy(),
  ];

  async executeWithDegradation<T>(
    operation: (options: any) => Promise<T>,
    initialOptions: any,
    retryOptions?: Partial<RetryOptions>
  ): Promise<T> {
    let currentOptions = { ...initialOptions };
    const appliedStrategies: string[] = [];

    return withRetry(async () => {
      try {
        return await operation(currentOptions);
      } catch (error) {
        if (!(error instanceof AIModelError)) {
          throw AIModelError.fromUnknown(error);
        }

        // Try to find applicable degradation strategy
        for (const strategy of this.degradationStrategies) {
          if (!appliedStrategies.includes(strategy.name) && 
              strategy.canApply(error, currentOptions)) {
            
            console.warn(`Applying degradation strategy: ${strategy.name}`);
            currentOptions = strategy.apply(currentOptions);
            appliedStrategies.push(strategy.name);
            
            // Update error to be retryable since we have a fallback
            throw new AIModelError({
              ...error,
              retryable: true,
              fallbackOptions: appliedStrategies,
            });
          }
        }

        // No more degradation strategies available
        throw error;
      }
    }, retryOptions);
  }
}

// Configuration validator
export const AIConfigValidator = {
  validateProviderConfig: (config: unknown): ProviderConfig => {
    return ProviderConfig.parse(config);
  },

  validateApiKey: (apiKey: string): boolean => {
    return typeof apiKey === 'string' && apiKey.length > 0;
  },

  validateEndpoint: (endpoint?: string): boolean => {
    if (!endpoint) return true;
    try {
      new URL(endpoint);
      return true;
    } catch {
      return false;
    }
  },

  validateTimeout: (timeout?: number): boolean => {
    return timeout === undefined || (typeof timeout === 'number' && timeout > 0);
  },
};

// Export singleton instance
export const aiProviderRegistry = AIProviderRegistry.getInstance();