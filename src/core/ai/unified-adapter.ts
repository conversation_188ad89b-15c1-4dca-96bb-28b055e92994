import { generateText, LanguageModel, CoreMessage } from 'ai';
import { z } from 'zod';
import { 
  createReasoningWrapper, 
  ReasoningModelWrapper, 
  ReasoningContentParser,
  ReasoningMiddlewareConfig 
} from './reasoning-middleware';
import { 
  structuredOutputManager,
  StructuredOutputManager 
} from './structured-output';
import { 
  ReasoningErrorHandler,
  ErrorContext,
  RecoveryStrategy,
  globalReasoningErrorHandler
} from './reasoning-error-handler';
import { 
  GenerationOptions, 
  GenerationResult, 
  ReasoningResult,
  Message,
  AIModelError,
  TokenUsage 
} from './interfaces';

/**
 * 统一模型调用适配器配置
 */
export interface UnifiedModelAdapterConfig {
  modelId: string;
  provider: string;
  apiKey?: string;
  endpoint?: string;
  enableReasoning?: boolean;
  enableStructuredOutput?: boolean;
  reasoningConfig?: Partial<ReasoningMiddlewareConfig>;
  defaultOptions?: Partial<GenerationOptions>;
}

/**
 * 适配器响应接口
 */
export interface AdapterResponse {
  content: string;
  reasoning?: string;
  usage: TokenUsage;
  metadata?: Record<string, any>;
}

/**
 * 结构化响应接口
 */
export interface StructuredAdapterResponse<T = any> {
  data: T;
  content: string;
  reasoning?: string;
  usage: TokenUsage;
  metadata?: Record<string, any>;
}

/**
 * 统一模型调用适配器
 * 
 * 提供统一的接口来调用不同类型的AI模型，
 * 自动处理推理模型的特殊输出格式，
 * 支持结构化输出和错误恢复机制
 */
export class UnifiedModelAdapter {
  private config: UnifiedModelAdapterConfig;
  private reasoningWrapper: ReasoningModelWrapper;
  private structuredManager: StructuredOutputManager;
  private errorHandler: ReasoningErrorHandler;
  private isReasoningModel: boolean;

  constructor(config: UnifiedModelAdapterConfig, errorHandler?: ReasoningErrorHandler) {
    this.config = config;
    
    // 初始化推理包装器
    this.reasoningWrapper = createReasoningWrapper(
      config.modelId, 
      config.reasoningConfig
    );
    this.isReasoningModel = this.reasoningWrapper.isReasoningModel(config.modelId);
    
    // 初始化结构化输出管理器
    this.structuredManager = structuredOutputManager;
    
    // 初始化错误处理器
    this.errorHandler = errorHandler || globalReasoningErrorHandler;
    
    // 根据配置更新推理设置
    if (config.enableReasoning !== undefined) {
      this.reasoningWrapper.updateConfig({ 
        enabled: config.enableReasoning && this.isReasoningModel 
      });
    }
  }

  /**
   * 获取适配器信息
   */
  getInfo(): {
    modelId: string;
    provider: string;
    isReasoningModel: boolean;
    reasoningEnabled: boolean;
    structuredOutputEnabled: boolean;
  } {
    return {
      modelId: this.config.modelId,
      provider: this.config.provider,
      isReasoningModel: this.isReasoningModel,
      reasoningEnabled: this.reasoningWrapper.getConfig().enabled || false,
      structuredOutputEnabled: this.config.enableStructuredOutput || false,
    };
  }

  /**
   * 创建语言模型实例
   * 根据配置创建相应的模型实例，并应用推理中间件（如果需要）
   */
  private async createLanguageModel(): Promise<LanguageModel> {
    // 这里需要根据provider创建对应的模型实例
    // 由于项目中已有 ai-providers.ts，我们需要与其集成
    throw new Error('Model creation should be handled by existing ai-providers.ts');
  }

  /**
   * 将内部Message转换为AI SDK的CoreMessage
   */
  private convertMessages(messages: Message[]): CoreMessage[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
    })) as CoreMessage[];
  }

  /**
   * 基础文本生成（带错误恢复机制）
   * 支持推理模型的思考过程提取和智能降级
   */
  async generateText(
    messages: Message[],
    options: GenerationOptions = {}
  ): Promise<AdapterResponse> {
    return this.executeWithRecovery(
      async (currentConfig) => this.generateTextInternal(messages, options, currentConfig),
      messages,
      options
    );
  }

  /**
   * 内部文本生成逻辑
   */
  private async generateTextInternal(
    messages: Message[],
    options: GenerationOptions,
    currentConfig: UnifiedModelAdapterConfig
  ): Promise<AdapterResponse> {
    const mergedOptions = { ...currentConfig.defaultOptions, ...options };
    
    // 模拟生成结果 - 实际实现中需要与现有的 ai-providers 集成
    const mockResult: GenerationResult = {
      content: "Mock generated content", // 这里应该是真实的AI响应
      usage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 },
      finishReason: 'stop'
    };

    // 如果是推理模型且启用推理功能
    if (this.isReasoningModel && currentConfig.enableReasoning) {
      const reasoningConfig = this.reasoningWrapper.getConfig();
      const reasoningParser = new ReasoningContentParser(reasoningConfig);
      
      const parsed = reasoningParser.parseReasoningContent(mockResult.content);
      
      if (reasoningParser.validateParsingResult(parsed)) {
        const cleanedText = reasoningParser.cleanFinalText(parsed.text);
        
        return {
          content: cleanedText,
          reasoning: parsed.reasoning,
          usage: mockResult.usage,
          metadata: {
            isReasoningModel: true,
            reasoningLength: parsed.reasoning?.length || 0,
            originalContentLength: mockResult.content.length,
            configUsed: {
              reasoningEnabled: true,
              structuredOutputEnabled: currentConfig.enableStructuredOutput
            }
          }
        };
      }
    }

    // 非推理模型或推理处理失败时的处理
    return {
      content: mockResult.content,
      usage: mockResult.usage,
      metadata: {
        isReasoningModel: this.isReasoningModel,
        reasoningEnabled: false,
        configUsed: {
          reasoningEnabled: false,
          structuredOutputEnabled: currentConfig.enableStructuredOutput
        }
      }
    };
  }

  /**
   * 执行带错误恢复的操作
   */
  private async executeWithRecovery<T>(
    operation: (config: UnifiedModelAdapterConfig) => Promise<T>,
    messages: Message[],
    options: GenerationOptions,
    attemptNumber: number = 1
  ): Promise<T> {
    let currentConfig = { ...this.config };
    
    try {
      return await operation(currentConfig);
    } catch (error) {
      // 构建错误上下文
      const errorContext: ErrorContext = {
        modelId: currentConfig.modelId,
        provider: currentConfig.provider,
        isReasoningModel: this.isReasoningModel,
        attemptNumber,
        originalError: error,
        userPrompt: messages.map(m => m.content).join(' '),
        enabledFeatures: {
          reasoning: currentConfig.enableReasoning || false,
          structuredOutput: currentConfig.enableStructuredOutput || false
        },
        timestamp: Date.now()
      };

      // 处理错误并获取恢复策略
      const recoveryResult = await this.errorHandler.handleError(
        error instanceof AIModelError ? error : AIModelError.fromUnknown(error),
        errorContext
      );

      console.log(`Recovery strategy: ${recoveryResult.strategy}, Success: ${recoveryResult.success}`);

      if (!recoveryResult.success || !recoveryResult.shouldRetry) {
        // 恢复失败或不应重试，抛出原错误
        throw error instanceof AIModelError ? error : AIModelError.fromUnknown(error);
      }

      // 应用恢复配置
      if (recoveryResult.newConfig) {
        if (recoveryResult.newConfig.reasoningEnabled !== undefined) {
          currentConfig.enableReasoning = recoveryResult.newConfig.reasoningEnabled;
          this.reasoningWrapper.updateConfig({ enabled: recoveryResult.newConfig.reasoningEnabled });
        }
        
        if (recoveryResult.newConfig.structuredOutputEnabled !== undefined) {
          currentConfig.enableStructuredOutput = recoveryResult.newConfig.structuredOutputEnabled;
        }
        
        if (recoveryResult.newConfig.modelId && recoveryResult.newConfig.provider) {
          currentConfig.modelId = recoveryResult.newConfig.modelId;
          currentConfig.provider = recoveryResult.newConfig.provider;
        }
      }

      // 递归重试（但不会超过最大重试次数，因为错误处理器会控制）
      return this.executeWithRecovery(operation, messages, options, attemptNumber + 1);
    }
  }

  /**
   * 结构化输出生成（带错误恢复机制）
   * 结合推理处理和结构化验证
   */
  async generateStructured<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    options: GenerationOptions = {}
  ): Promise<StructuredAdapterResponse<T>> {
    return this.executeWithRecovery(
      async (currentConfig) => this.generateStructuredInternal(messages, schema, options, currentConfig),
      messages,
      options
    );
  }

  /**
   * 内部结构化输出生成逻辑
   */
  private async generateStructuredInternal<T>(
    messages: Message[],
    schema: z.ZodSchema<T>,
    options: GenerationOptions,
    currentConfig: UnifiedModelAdapterConfig
  ): Promise<StructuredAdapterResponse<T>> {
    if (!currentConfig.enableStructuredOutput) {
      throw new AIModelError({
        type: 'CONFIGURATION_ERROR',
        message: 'Structured output is not enabled for this adapter',
        retryable: false
      });
    }

    const mergedOptions = { 
      ...currentConfig.defaultOptions, 
      ...options,
      enableStructuredOutput: true 
    };

    // 模拟生成器函数 - 实际实现中需要与现有的 ai-providers 集成
    const mockGenerator = async (
      msgs: Message[], 
      opts: GenerationOptions
    ): Promise<GenerationResult> => {
      return {
        content: JSON.stringify({ example: "Mock structured data" }), // 模拟JSON响应
        usage: { promptTokens: 120, completionTokens: 80, totalTokens: 200 },
        finishReason: 'stop'
      };
    };

    // 如果是推理模型，使用推理感知的结构化输出
    if (this.isReasoningModel && currentConfig.enableReasoning) {
      const result = await this.structuredManager.generateStructuredWithReasoning(
        messages,
        schema,
        mockGenerator,
        mergedOptions,
        this.reasoningWrapper.getConfig()
      );

      return {
        data: result.data,
        content: result.result.content,
        reasoning: result.reasoning,
        usage: result.result.usage,
        metadata: {
          isReasoningModel: true,
          hasReasoning: !!result.reasoning,
          strategy: 'reasoning-aware',
          configUsed: currentConfig
        }
      };
    } else {
      // 使用标准结构化输出
      const result = await this.structuredManager.generateStructured(
        messages,
        schema,
        mockGenerator,
        mergedOptions
      );

      return {
        data: result.data,
        content: result.result.content,
        usage: result.result.usage,
        metadata: {
          isReasoningModel: false,
          strategy: 'standard',
          configUsed: currentConfig
        }
      };
    }
  }

  /**
   * 批量处理多个请求
   * 支持并发处理以提高性能
   */
  async generateBatch(
    requests: Array<{
      messages: Message[];
      options?: GenerationOptions;
      id?: string;
    }>
  ): Promise<Array<AdapterResponse & { id?: string }>> {
    const results = await Promise.allSettled(
      requests.map(async (request, index) => {
        const result = await this.generateText(request.messages, request.options);
        return {
          ...result,
          id: request.id || `batch-${index}`
        };
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        // 处理失败的请求
        return {
          content: '',
          usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          id: requests[index].id || `batch-${index}`,
          metadata: {
            error: true,
            errorMessage: result.reason instanceof Error ? result.reason.message : String(result.reason)
          }
        };
      }
    });
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<UnifiedModelAdapterConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新推理配置
    if (newConfig.reasoningConfig || newConfig.enableReasoning !== undefined) {
      this.reasoningWrapper.updateConfig({
        ...newConfig.reasoningConfig,
        enabled: newConfig.enableReasoning !== undefined 
          ? newConfig.enableReasoning && this.isReasoningModel
          : this.reasoningWrapper.getConfig().enabled
      });
    }
  }

  /**
   * 健康检查
   * 验证适配器是否可用
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: Record<string, any>;
  }> {
    try {
      // 简单的健康检查 - 尝试生成一个短响应
      const testMessages: Message[] = [{
        role: 'user',
        content: 'Hello',
        timestamp: Date.now()
      }];

      const result = await this.generateText(testMessages, { 
        maxTokens: 10,
        temperature: 0.1 
      });

      return {
        status: 'healthy',
        details: {
          modelId: this.config.modelId,
          provider: this.config.provider,
          isReasoningModel: this.isReasoningModel,
          responseLength: result.content.length,
          hasReasoning: !!result.reasoning,
          usage: result.usage
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : String(error),
          modelId: this.config.modelId,
          provider: this.config.provider
        }
      };
    }
  }
}

/**
 * 创建统一模型适配器的工厂函数
 */
export function createUnifiedModelAdapter(
  config: UnifiedModelAdapterConfig
): UnifiedModelAdapter {
  return new UnifiedModelAdapter(config);
}

/**
 * 适配器管理器
 * 管理多个模型适配器实例
 */
export class AdapterManager {
  private adapters: Map<string, UnifiedModelAdapter> = new Map();

  /**
   * 注册适配器
   */
  register(name: string, adapter: UnifiedModelAdapter): void {
    this.adapters.set(name, adapter);
  }

  /**
   * 获取适配器
   */
  get(name: string): UnifiedModelAdapter | undefined {
    return this.adapters.get(name);
  }

  /**
   * 移除适配器
   */
  remove(name: string): boolean {
    return this.adapters.delete(name);
  }

  /**
   * 获取所有适配器信息
   */
  getAllAdapters(): Array<{ name: string; info: ReturnType<UnifiedModelAdapter['getInfo']> }> {
    return Array.from(this.adapters.entries()).map(([name, adapter]) => ({
      name,
      info: adapter.getInfo()
    }));
  }

  /**
   * 批量健康检查
   */
  async healthCheckAll(): Promise<Record<string, Awaited<ReturnType<UnifiedModelAdapter['healthCheck']>>>> {
    const results: Record<string, Awaited<ReturnType<UnifiedModelAdapter['healthCheck']>>> = {};
    
    await Promise.all(
      Array.from(this.adapters.entries()).map(async ([name, adapter]) => {
        try {
          results[name] = await adapter.healthCheck();
        } catch (error) {
          results[name] = {
            status: 'unhealthy',
            details: {
              error: error instanceof Error ? error.message : String(error)
            }
          };
        }
      })
    );

    return results;
  }
}

// 全局适配器管理器实例
export const globalAdapterManager = new AdapterManager();