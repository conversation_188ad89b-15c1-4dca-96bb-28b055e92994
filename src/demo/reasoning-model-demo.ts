import { 
  createFormFillingAdapter,
  SmartAdapterFactory,
  getAdapterDebugInfo 
} from '../core/ai/smart-adapter-factory';
import { z } from 'zod';
import { Message } from '../core/ai/interfaces';

/**
 * 推理模型处理系统演示
 * 展示如何在实际表单填充场景中使用新的推理处理功能
 */

// 表单数据Schema定义
const ContactFormSchema = z.object({
  name: z.string().min(1, "姓名不能为空"),
  email: z.string().email("邮箱格式不正确"), 
  phone: z.string().min(10, "电话号码至少10位"),
  company: z.string().optional(),
  position: z.string().optional(),
  message: z.string().min(10, "消息至少10个字符")
});

const BugReportSchema = z.object({
  title: z.string().min(5, "标题至少5个字符"),
  description: z.string().min(20, "描述至少20个字符"),
  severity: z.enum(["low", "medium", "high", "critical"]),
  steps: z.string().min(10, "复现步骤至少10个字符"),
  environment: z.string(),
  expectedBehavior: z.string().optional(),
  actualBehavior: z.string().optional()
});

/**
 * 推理模型演示类
 */
export class ReasoningModelDemo {
  
  /**
   * 演示联系表单填充
   */
  static async demoContactFormFilling() {
    console.log('📝 演示：联系表单填充（推理模型）');
    console.log('=====================================\n');

    try {
      // 创建推理感知的适配器
      const adapter = await createFormFillingAdapter(
        'deepseek-reasoner', // 使用DeepSeek推理模型
        process.env.DEEPSEEK_API_KEY || 'demo-key'
      );

      // 显示适配器信息
      const debugInfo = getAdapterDebugInfo(adapter);
      console.log('🔧 适配器配置信息:');
      console.log(`   模型: ${debugInfo.modelId}`);
      console.log(`   提供商: ${debugInfo.provider}`);
      console.log(`   推理模型: ${debugInfo.isReasoningModel ? '是' : '否'}`);
      console.log(`   推理功能: ${debugInfo.reasoningEnabled ? '启用' : '禁用'}\n`);

      // 模拟用户输入
      const userInput: Message[] = [{
        role: 'user',
        content: '我是李明，想要联系你们公司了解AI产品。我在腾讯工作，担任产品经理。我的邮箱是******************，电话是13800138000。希望能够预约一次产品演示。',
        timestamp: Date.now()
      }];

      console.log('💬 用户输入:');
      console.log(`   "${userInput[0].content}"\n`);

      // 生成结构化表单数据
      console.log('⚡ 正在使用推理模型处理...\n');
      
      const result = await adapter.generateStructured(
        userInput,
        ContactFormSchema
      );

      // 显示结果
      console.log('✨ 处理结果:');
      console.log('-------------');
      
      if (result.reasoning) {
        console.log('🧠 模型推理过程:');
        console.log(`   ${result.reasoning.substring(0, 200)}${result.reasoning.length > 200 ? '...' : ''}\n`);
      }

      console.log('📊 提取的表单数据:');
      console.log('   姓名:', result.data.name);
      console.log('   邮箱:', result.data.email);
      console.log('   电话:', result.data.phone);
      console.log('   公司:', result.data.company || '未提供');
      console.log('   职位:', result.data.position || '未提供');
      console.log('   消息:', result.data.message.substring(0, 50) + '...');

      console.log('\n📈 性能指标:');
      console.log('   Token使用:', `${result.usage.totalTokens} (输入: ${result.usage.promptTokens}, 输出: ${result.usage.completionTokens})`);
      console.log('   处理策略:', result.metadata?.strategy || '未知');

      return result;

    } catch (error) {
      console.error('❌ 联系表单填充演示失败:', error);
      throw error;
    }
  }

  /**
   * 演示Bug报告填充
   */
  static async demoBugReportFilling() {
    console.log('\n🐛 演示：Bug报告填充（推理模型）');
    console.log('=====================================\n');

    try {
      // 创建适配器（使用不同的推理模型）
      const adapter = await createFormFillingAdapter(
        'o1-preview', // 使用OpenAI O1推理模型
        process.env.OPENAI_API_KEY || 'demo-key'
      );

      const userInput: Message[] = [{
        role: 'user', 
        content: '我在使用你们的登录功能时遇到问题。当我输入正确的用户名和密码后，点击登录按钮，页面会卡住大约10秒，然后显示"服务器错误"。我使用的是Chrome浏览器，Windows 11系统。我期望能够正常登录到系统中。这个问题每次都会出现，比较严重。',
        timestamp: Date.now()
      }];

      console.log('💬 用户描述:');
      console.log(`   "${userInput[0].content}"\n`);

      console.log('⚡ 正在使用O1推理模型分析Bug...\n');

      const result = await adapter.generateStructured(
        userInput,
        BugReportSchema
      );

      console.log('✨ 生成的Bug报告:');
      console.log('------------------');

      if (result.reasoning) {
        console.log('🧠 模型分析过程:');
        console.log(`   ${result.reasoning.substring(0, 300)}${result.reasoning.length > 300 ? '...' : ''}\n`);
      }

      console.log('📋 结构化Bug报告:');
      console.log('   标题:', result.data.title);
      console.log('   严重程度:', result.data.severity);
      console.log('   描述:', result.data.description.substring(0, 100) + '...');
      console.log('   复现步骤:', result.data.steps);
      console.log('   环境信息:', result.data.environment);
      console.log('   期望行为:', result.data.expectedBehavior || '未指定');
      console.log('   实际行为:', result.data.actualBehavior || '未指定');

      return result;

    } catch (error) {
      console.error('❌ Bug报告填充演示失败:', error);
      throw error;
    }
  }

  /**
   * 演示错误恢复机制
   */
  static async demoErrorRecovery() {
    console.log('\n🔄 演示：错误恢复机制');
    console.log('========================\n');

    try {
      const adapter = await createFormFillingAdapter(
        'deepseek-reasoner',
        'invalid-api-key' // 故意使用无效的API密钥
      );

      console.log('🚨 模拟API密钥错误场景...\n');

      const userInput: Message[] = [{
        role: 'user',
        content: '填充一个简单的联系表单',
        timestamp: Date.now()
      }];

      // 这应该触发错误恢复机制
      const result = await adapter.generateStructured(
        userInput,
        ContactFormSchema
      );

      console.log('🎯 错误恢复成功！');
      console.log('   使用的恢复策略:', result.metadata?.recoveryStrategy || '未知');
      console.log('   最终使用的模型:', result.metadata?.finalModelUsed || '未知');

    } catch (error) {
      console.log('⚠️  错误恢复演示捕获到预期错误:');
      console.log(`   错误类型: ${error instanceof Error ? error.constructor.name : typeof error}`);
      console.log(`   错误信息: ${error instanceof Error ? error.message : String(error)}`);
      console.log('   这表明错误处理机制正在工作');
    }
  }

  /**
   * 演示性能对比（推理模型 vs 普通模型）
   */
  static async demoPerformanceComparison() {
    console.log('\n⚡ 演示：性能对比（推理 vs 普通模型）');
    console.log('=========================================\n');

    const userInput: Message[] = [{
      role: 'user',
      content: '我需要填充一个包含姓名、邮箱、电话的联系表单，我叫张伟，在北京工作，邮箱是********************',
      timestamp: Date.now()
    }];

    // 测试推理模型
    console.log('🧠 测试推理模型 (DeepSeek Reasoner)...');
    const reasoningStart = Date.now();
    
    try {
      const reasoningAdapter = await createFormFillingAdapter('deepseek-reasoner', 'demo-key');
      const reasoningResult = await reasoningAdapter.generateStructured(userInput, ContactFormSchema);
      const reasoningDuration = Date.now() - reasoningStart;

      console.log(`   处理时间: ${reasoningDuration}ms`);
      console.log(`   Token使用: ${reasoningResult.usage.totalTokens}`);
      console.log(`   包含推理: ${reasoningResult.reasoning ? '是' : '否'}`);
      
    } catch (error) {
      console.log(`   处理失败: ${error instanceof Error ? error.message : error}`);
    }

    // 测试普通模型
    console.log('\n🤖 测试普通模型 (GPT-4)...');
    const standardStart = Date.now();
    
    try {
      const standardAdapter = await createFormFillingAdapter('gpt-4', 'demo-key');
      const standardResult = await standardAdapter.generateStructured(userInput, ContactFormSchema);
      const standardDuration = Date.now() - standardStart;

      console.log(`   处理时间: ${standardDuration}ms`);
      console.log(`   Token使用: ${standardResult.usage.totalTokens}`);
      console.log(`   包含推理: ${standardResult.reasoning ? '是' : '否'}`);

    } catch (error) {
      console.log(`   处理失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 运行完整演示
   */
  static async runCompleteDemo() {
    console.log('🚀 Fillify 推理模型处理系统 - 完整演示');
    console.log('==========================================');
    console.log('展示推理模型在表单填充中的实际应用...\n');

    try {
      // 1. 联系表单填充演示
      await this.demoContactFormFilling();
      await this.sleep(1000);

      // 2. Bug报告填充演示  
      await this.demoBugReportFilling();
      await this.sleep(1000);

      // 3. 错误恢复演示
      await this.demoErrorRecovery();
      await this.sleep(1000);

      // 4. 性能对比演示
      await this.demoPerformanceComparison();

      console.log('\n🎉 完整演示结束！');
      console.log('================');
      console.log('✅ 推理模型处理系统已准备就绪');
      console.log('✅ 支持智能表单填充');  
      console.log('✅ 具备错误恢复机制');
      console.log('✅ 提供详细的处理信息');

      console.log('\n📝 使用建议:');
      console.log('------------');
      console.log('• 对于复杂的表单填充任务，推荐使用推理模型');
      console.log('• 推理模型提供更好的理解和推断能力');
      console.log('• 系统会自动处理错误并降级到稳定的处理方式');
      console.log('• 可以通过调试信息监控处理过程和性能');

    } catch (error) {
      console.error('\n❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 工具函数：延迟
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出便捷函数
export const runReasoningModelDemo = () => ReasoningModelDemo.runCompleteDemo();