// 类型定义
import { CookieChangeInfo } from '../src/types'
import { createAIProvider, AIProviderError } from '../src/services/ai-providers'
import { generatePrompt, cleanAIResponse, FormRequest } from '../src/services/prompt-generator'
import { 
  createReasoningWrapper, 
  ReasoningContentParser, 
  ReasoningMiddlewareConfig 
} from '../src/core/ai/reasoning-middleware'
import { 
  structuredOutputManager, 
  StructuredOutputManager 
} from '../src/core/ai/structured-output'
import {
  parseReasoningContent,
  EnhancedReasoningParser,
  ReasoningModelType
} from '../src/core/ai/enhanced-reasoning-parser'
import { StreamProcessor } from '../src/core/streaming/StreamProcessor'
import { 
  MiddlewarePipeline,
  createDefaultPipeline,
  MiddlewareContext,
  ResponseMiddlewareContext
} from '../src/core/ai/middleware-pipeline'
import {
  ErrorRecoveryManager,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  SmartRetryHandler
} from '../src/core/ai/error-recovery'
import { z } from 'zod'

interface Logger {
  info: (...args: any[]) => void
  error: (...args: any[]) => void
  success: (...args: any[]) => void
  warn: (...args: any[]) => void
}

interface UserInfo {
  id: string
  [key: string]: any
}

interface Settings {
  useCustomApi: boolean
  defaultProvider: string
  defaultModel: string
  ollama_endpoint?: string
}

interface ApiKeys {
  [provider: string]: string
}



interface Message {
  type: string
  [key: string]: any
}

interface DefaultModels {
  openai: string
  claude: string
  moonshot: string
  gemini: string
  openrouter: string
  ollama: string
}

// Background script
export default defineBackground(() => {
  // Helper function for logging - enabled for debugging reasoning models
  const Logger: Logger = {
    info: (...args) => console.log('[Fillify Info]', ...args),
    error: (...args) => console.error('[Formify Error]', ...args),
    success: (...args) => console.log('[Fillify Success]', ...args),
    warn: (...args) => console.warn('[Fillify Warn]', ...args)
  }

  // JSON schema for form content validation
  const FormContentSchema = z.record(z.string());

  // 创建全局中间件管道和错误恢复管理器
  const middlewarePipeline = createDefaultPipeline();
  const errorRecoveryManager = createDefaultRecoveryManager();

  /**
   * 处理流式AI请求
   */
  async function handleStreamingAiRequest(
    aiProvider: any,
    prompt: string,
    request: any,
    sendResponse: (response: any) => void,
    model: string,
    provider: string
  ): Promise<any> {
    try {
      Logger.info('Starting streaming AI request with new processor:', {
        provider: aiProvider.constructor.name,
        model: request.model
      });

      let isFirstChunk = true;
      let finalContent = '';
      let finalReasoning = '';
      let streamUsage = null; // 用于存储流式响应的使用统计

      // 创建流式处理器
      const streamProcessor = new StreamProcessor({
        enableReasoning: true,
        onUpdate: (content: string, reasoning?: string) => {
          // 实时更新回调
          if (isFirstChunk) {
            // 第一个更新，通过 sendResponse 发送
            Logger.info('Sending first streaming update:', {
              contentLength: content.length,
              reasoningLength: reasoning?.length || 0
            });

            sendResponse({
              success: true,
              streaming: true,
              content: content,
              reasoning: reasoning,
              isComplete: false
            });
            isFirstChunk = false;
          } else {
            // 后续更新，通过消息发送到 content script
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
              if (tabs[0]?.id) {
                chrome.tabs.sendMessage(tabs[0].id, {
                  type: 'streamingUpdate',
                  content: content,
                  reasoning: reasoning,
                  isComplete: false
                });
              }
            });
          }
        },
        onComplete: (content: string, reasoning?: string) => {
          finalContent = content;
          finalReasoning = reasoning || '';
          Logger.info('Stream processing complete:', {
            contentLength: content.length,
            reasoningLength: reasoning?.length || 0
          });
        },
        onError: (error: Error) => {
          Logger.error('Stream processing error:', error);
        }
      });

      // 获取流式响应并处理
      const stream = await aiProvider.generateContentStream(prompt);

      for await (const chunk of stream) {
        Logger.info('Processing stream chunk:', {
          deltaLength: chunk.delta?.length || 0,
          isComplete: chunk.isComplete
        });

        // 收集 usage 信息（通常在最后一个 chunk 中）
        if (chunk.usage) {
          streamUsage = chunk.usage;
        }

        // 使用流式处理器处理数据块
        streamProcessor.processChunk({
          delta: chunk.delta || '',
          isComplete: chunk.isComplete
        });

        if (chunk.isComplete) {
          break;
        }
      }

      // 流式完成后，进行最终处理
      Logger.info('Processing final response:', {
        contentLength: finalContent.length,
        reasoningLength: finalReasoning.length
      });

      const processedResponse = await processAIResponse(
        finalContent,
        model,
        provider,
        {
          enableReasoning: true,
          enableStructuredOutput: true,
          responseSchema: FormContentSchema,
        }
      );

      // 验证表单内容格式
      let validatedContent;
      try {
        validatedContent = FormContentSchema.parse(JSON.parse(processedResponse.content));
      } catch (validationError) {
        Logger.warn('Content validation failed in streaming:', validationError);
        // 如果验证失败，尝试使用原始内容
        try {
          validatedContent = JSON.parse(processedResponse.content);
        } catch {
          validatedContent = {};
        }
      }

      // 更新token使用统计 (跳过 Ollama，因为它是本地运行的)
      if (streamUsage && provider !== 'ollama') {
        try {
          // 获取当前token统计
          const tokenStatsStorage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS)
          const tokenStats = tokenStatsStorage[STORAGE_KEYS.TOKEN_STATS] || {}

          // 如果没有该provider的统计，初始化
          if (!tokenStats[provider]) {
            tokenStats[provider] = {
              promptTokens: 0,
              completionTokens: 0,
              totalTokens: 0,
              lastUpdated: new Date().toISOString()
            }
          }

          // 确保统计对象具有正确的结构
          if (!tokenStats[provider].promptTokens) tokenStats[provider].promptTokens = 0
          if (!tokenStats[provider].completionTokens) tokenStats[provider].completionTokens = 0
          if (!tokenStats[provider].totalTokens) tokenStats[provider].totalTokens = 0

          // 使用AI响应中的usage数据
          const usage = streamUsage
          const promptTokens = usage.prompt_tokens || 0
          const completionTokens = usage.completion_tokens || 0
          const totalTokens = usage.total_tokens || (promptTokens + completionTokens)

          // 更新统计数据
          tokenStats[provider].promptTokens = (tokenStats[provider].promptTokens || 0) + promptTokens
          tokenStats[provider].completionTokens = (tokenStats[provider].completionTokens || 0) + completionTokens
          tokenStats[provider].totalTokens = (tokenStats[provider].totalTokens || 0) + totalTokens
          tokenStats[provider].lastUpdated = new Date().toISOString()

          Logger.info('Updated token stats with streaming usage data:', {
            provider,
            promptTokens,
            completionTokens,
            totalTokens,
            accumulatedTotal: tokenStats[provider].totalTokens
          })

          // 保存更新后的统计
          await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: tokenStats })
          Logger.info('Token usage statistics updated for', provider, tokenStats[provider])
        } catch (statsError) {
          Logger.error('Error updating token statistics in streaming:', statsError)
          // 不影响主流程，继续返回AI响应
        }
      }

      // 发送最终完成消息
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]?.id) {
          chrome.tabs.sendMessage(tabs[0].id, {
            type: 'streamingComplete',
            content: processedResponse.content,
            data: validatedContent, // 使用验证后的数据对象
            reasoning: finalReasoning,
            metadata: processedResponse.metadata
          });
        }
      });

      return {
        success: true,
        streaming: true,
        content: processedResponse.content,
        data: validatedContent, // 使用验证后的数据对象
        reasoning: finalReasoning,
        metadata: processedResponse.metadata
      };

    } catch (error) {
      Logger.error('Streaming AI request failed:', error);
      throw error;
    }
  }

  /**
   * 处理非流式AI请求
   */
  async function handleNonStreamingAiRequest(
    aiResponse: any,
    model: string,
    provider: string,
    request: any
  ): Promise<any> {
    // 智能处理AI响应（支持推理模型）
    // 确保我们传递的是字符串内容而不是整个响应对象
    const contentToProcess = typeof aiResponse === 'string' ? aiResponse : aiResponse.content;
    const processedResponse = await processAIResponse(
      contentToProcess,
      model,
      provider,
      {
        enableReasoning: true,
        enableStructuredOutput: true,
        responseSchema: FormContentSchema,
      }
    );

    Logger.info('Processed response:', processedResponse);

    // 验证生成的内容
    let validatedContent;
    try {
      validatedContent = FormContentSchema.parse(JSON.parse(processedResponse.content));
      Logger.info('Content validation successful:', validatedContent);
    } catch (validationError) {
      Logger.warn('Content validation failed:', validationError);
      throw new Error(`Generated content validation failed: ${validationError instanceof Error ? validationError.message : String(validationError)}`);
    }

    // 构建响应数据
    const responseData = {
      content: processedResponse.content,
      data: validatedContent,
      usage: aiResponse.usage,
      metadata: processedResponse.metadata,
      ...(processedResponse.reasoning && { reasoning: processedResponse.reasoning }),
      ...(processedResponse.warnings && processedResponse.warnings.length > 0 && { warnings: processedResponse.warnings })
    };

    // 更新token使用统计 (跳过 Ollama，因为它是本地运行的)
    if (aiResponse.usage && provider !== 'ollama') {
      try {
        // 获取当前token统计
        const tokenStatsStorage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS)
        const tokenStats = tokenStatsStorage[STORAGE_KEYS.TOKEN_STATS] || {}

        // 如果没有该provider的统计，初始化
        if (!tokenStats[provider]) {
          tokenStats[provider] = {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
            lastUpdated: new Date().toISOString()
          }
        }

        // 确保统计对象具有正确的结构
        if (!tokenStats[provider].promptTokens) tokenStats[provider].promptTokens = 0
        if (!tokenStats[provider].completionTokens) tokenStats[provider].completionTokens = 0
        if (!tokenStats[provider].totalTokens) tokenStats[provider].totalTokens = 0

        // 使用AI响应中的usage数据
        const usage = aiResponse.usage
        const promptTokens = usage.prompt_tokens || 0
        const completionTokens = usage.completion_tokens || 0
        const totalTokens = usage.total_tokens || (promptTokens + completionTokens)

        // 更新统计数据
        tokenStats[provider].promptTokens = (tokenStats[provider].promptTokens || 0) + promptTokens
        tokenStats[provider].completionTokens = (tokenStats[provider].completionTokens || 0) + completionTokens
        tokenStats[provider].totalTokens = (tokenStats[provider].totalTokens || 0) + totalTokens
        tokenStats[provider].lastUpdated = new Date().toISOString()

        Logger.info('Updated token stats with actual usage data:', {
          provider,
          promptTokens,
          completionTokens,
          totalTokens,
          accumulatedTotal: tokenStats[provider].totalTokens
        })

        // 保存更新后的统计
        await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: tokenStats })
        Logger.info('Token usage statistics updated for', provider, tokenStats[provider])
      } catch (statsError) {
        Logger.error('Error updating token statistics:', statsError)
        // 不影响主流程，继续返回AI响应
      }
    }

    Logger.info('Final response data:', responseData);
    return { success: true, ...responseData };
  }

  /**
   * 预处理AI响应内容，处理Unicode转义和其他格式问题
   * @param content 原始内容
   * @returns 预处理后的内容
   */
  function preprocessAIContent(content: string): string {
    try {
      // 1. 处理Unicode转义的推理标签
      let processedContent = content
        // 处理 \u003c 和 \u003e (< 和 >)
        .replace(/\\u003c/g, '<')
        .replace(/\\u003e/g, '>')
        // 处理其他常见的Unicode转义
        .replace(/\\u0022/g, '"')  // 双引号
        .replace(/\\u0027/g, "'")  // 单引号
        .replace(/\\u002f/g, '/')  // 斜杠
        .replace(/\\u005c/g, '\\') // 反斜杠

      // 2. 处理可能的JSON字符串转义
      // 如果整个内容被包装在JSON字符串中，尝试解析
      if (processedContent.startsWith('"') && processedContent.endsWith('"')) {
        try {
          processedContent = JSON.parse(processedContent);
        } catch {
          // 如果解析失败，保持原样
        }
      }

      // 3. 处理可能的多层转义
      // 有些API可能会进行多次转义
      let previousContent = '';
      let iterations = 0;
      while (previousContent !== processedContent && iterations < 3) {
        previousContent = processedContent;
        processedContent = processedContent
          .replace(/\\u003c/g, '<')
          .replace(/\\u003e/g, '>');
        iterations++;
      }

      Logger.info('Content preprocessing completed', {
        originalLength: content.length,
        processedLength: processedContent.length,
        hasReasoningTags: /<(think|thinking|reasoning)>/i.test(processedContent),
        iterations
      });

      return processedContent;
    } catch (error) {
      Logger.warn('Content preprocessing failed, using original content:', error);
      return content;
    }
  }

  /**
   * 增强的AI响应处理器，支持多种推理模型和错误恢复
   * @param content AI生成的原始内容
   * @param modelId 模型ID
   * @param provider 提供商
   * @param options 可选的处理选项
   * @returns 处理后的结果，包含内容、推理和元数据
   */
  async function processAIResponse(
    content: string, 
    modelId: string, 
    provider: string,
    options: {
      enableReasoning?: boolean;
      enableStructuredOutput?: boolean;
      responseSchema?: any;
      maxRetries?: number;
    } = {}
  ): Promise<{ 
    content: string; 
    reasoning?: string; 
    metadata?: any;
    warnings?: string[];
  }> {
    try {
      Logger.info('Processing AI response with enhanced pipeline', {
        modelId,
        provider,
        contentLength: content.length,
        options
      });

      // 预处理内容，处理Unicode转义等问题
      const preprocessedContent = preprocessAIContent(content);

      // 使用增强推理解析器进行内容解析
      const reasoningResult = parseReasoningContent(preprocessedContent, modelId, {
        minReasoningLength: 10,
      });

      Logger.info('Enhanced reasoning parsing completed', {
        modelType: reasoningResult.metadata.modelType,
        parseMethod: reasoningResult.metadata.parseMethod,
        confidence: reasoningResult.metadata.confidence,
        hasReasoning: !!reasoningResult.reasoning,
        warnings: reasoningResult.metadata.warnings
      });

      // 清理和处理最终文本内容
      let processedContent = reasoningResult.text;
      
      // 如果启用了结构化输出，尝试提取和验证JSON
      if (options.enableStructuredOutput) {
        try {
          const jsonData = extractAndValidateJSON(processedContent, options.responseSchema);
          if (jsonData) {
            processedContent = JSON.stringify(jsonData);
            Logger.info('Structured output validation successful');
          }
        } catch (error) {
          Logger.warn('Structured output validation failed, using cleaned text:', error);
          processedContent = cleanAIResponse(processedContent);
        }
      } else {
        processedContent = cleanAIResponse(processedContent);
      }

      // 构建返回结果
      const result = {
        content: processedContent,
        reasoning: reasoningResult.reasoning,
        metadata: {
          modelType: reasoningResult.metadata.modelType,
          parseMethod: reasoningResult.metadata.parseMethod,
          confidence: reasoningResult.metadata.confidence,
          reasoningTokens: reasoningResult.metadata.reasoningTokens,
          textTokens: reasoningResult.metadata.textTokens,
          processingTime: Date.now(),
        },
        warnings: reasoningResult.metadata.warnings
      };

      Logger.success('AI response processing completed successfully', {
        hasReasoning: !!result.reasoning,
        contentLength: result.content.length,
        confidence: result.metadata.confidence,
        warningCount: result.warnings?.length || 0
      });

      return result;
      
    } catch (error) {
      Logger.error('Enhanced AI response processing failed:', error);
      
      // 降级到简单处理
      try {
        Logger.info('Attempting fallback to simple processing');
        const fallbackContent = cleanAIResponse(content);
        
        return {
          content: fallbackContent,
          metadata: {
            modelType: 'unknown',
            parseMethod: 'fallback',
            confidence: 0.5,
            processingTime: Date.now(),
          },
          warnings: [`Enhanced processing failed: ${error instanceof Error ? error.message : String(error)}`]
        };
      } catch (fallbackError) {
        Logger.error('Fallback processing also failed:', fallbackError);
        throw new Error(`Failed to process AI response: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * 提取和验证JSON数据的辅助方法
   */
  function extractAndValidateJSON(content: string, schema?: any): any {
    // 尝试多种JSON提取策略
    const jsonPatterns = [
      /```json\s*(\{.*?\})\s*```/gs,
      /```\s*(\{.*?\})\s*```/gs,
      /(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/gs,
    ];

    let jsonData = null;

    // 首先尝试直接解析
    try {
      jsonData = JSON.parse(content);
    } catch {
      // 尝试模式匹配
      for (const pattern of jsonPatterns) {
        const matches = content.match(pattern);
        if (matches) {
          for (const match of matches) {
            try {
              const extracted = match.replace(/```(json)?\s*|\s*```/g, '').trim();
              jsonData = JSON.parse(extracted);
              break;
            } catch {
              continue;
            }
          }
          if (jsonData) break;
        }
      }
    }

    if (!jsonData) {
      throw new Error('No valid JSON found in content');
    }

    // 如果提供了schema，进行验证
    if (schema) {
      try {
        return schema.parse(jsonData);
      } catch (error) {
        throw new Error(`JSON schema validation failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return jsonData;
  }

  /**
   * 验证和清理表单内容
   * @param contentStr JSON字符串
   * @returns 验证后的表单数据
   */
  function validateFormContent(contentStr: string): Record<string, string> {
    try {
      // 解析JSON
      const parsed = JSON.parse(contentStr);
      
      // 使用Zod验证
      const validated = FormContentSchema.parse(parsed);
      
      // 过滤掉空值和无效值
      const cleaned = Object.entries(validated)
        .filter(([_, value]) => value && typeof value === 'string' && value.trim().length > 0)
        .reduce((acc, [key, value]) => {
          acc[key] = value.trim();
          return acc;
        }, {} as Record<string, string>);
      
      return cleaned;
    } catch (error) {
      Logger.error('Failed to validate form content:', error);
      throw new Error(`Invalid form content format: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Constants
  const STORAGE_KEYS = {
    SETTINGS: 'formify_settings',
    API_KEYS: 'formify_api_keys',
    VALIDATED_KEYS: 'formify_validated_keys',
    SKIP_LOGIN: 'formify_skip_login',
    TOKEN_STATS: 'formify_token_stats',
    PROJECTS: 'formify_projects'
  } as const

  // API Base URL and Cookie Domain
  const API_BASE_URL = 'https://fillify-343190162770.asia-east1.run.app/api'
  const COOKIE_DOMAIN = 'fillify.tech'
  const COOKIE_URL = 'https://fillify.tech'

  // 全局登录状态管理
  let isLoggedIn = false
  let skipLogin = false
  
  // 页面状态缓存
  const pageStatusCache = new Map<number, {
    isValid: boolean;
    hasFormFields: boolean;
    needsRefresh: boolean;
    lastChecked: number;
  }>();

  // 页面状态缓存过期时间（10秒）
  const PAGE_STATUS_CACHE_TTL = 10 * 1000;

  // 初始化登录状态 - 立即执行
  async function initializeLoginStatus() {
    try {
      // 检查是否已选择跳过登录
      const { formify_skip_login } = await chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN)
      skipLogin = !!formify_skip_login
      
      // 立即检查登录状态
      await checkLoginStatus()
      
      Logger.info('Login status initialized:', { isLoggedIn, skipLogin })
    } catch (error) {
      Logger.error('Error initializing login status:', error)
    }
  }

  // 立即执行初始化
  initializeLoginStatus()

  // 检查页面是否支持inpage popup
  async function checkPageSupport(tabId: number, url?: string): Promise<{
    isValid: boolean;
    hasFormFields: boolean;
    needsRefresh: boolean;
    error?: string;
  }> {
    try {
      // 检查缓存
      const cached = pageStatusCache.get(tabId);
      if (cached && Date.now() - cached.lastChecked < PAGE_STATUS_CACHE_TTL) {
        return {
          isValid: cached.isValid,
          hasFormFields: cached.hasFormFields,
          needsRefresh: cached.needsRefresh
        };
      }

      // 检查是否为特殊页面
      if (url && (
        url.startsWith('chrome://') ||
        url.startsWith('chrome-extension://') ||
        url.startsWith('edge://') ||
        url.startsWith('about:') ||
        url.startsWith('moz-extension://')
      )) {
        const result = {
          isValid: false,
          hasFormFields: false,
          needsRefresh: false,
          error: 'special_page'
        };
        pageStatusCache.set(tabId, { ...result, lastChecked: Date.now() });
        return result;
      }

      // 向content script查询页面状态
      try {
        const response = await chrome.tabs.sendMessage(tabId, { type: 'checkPageStatus' });
        const result = {
          isValid: response?.isValid || false,
          hasFormFields: response?.hasFormFields || false,
          needsRefresh: response?.needsRefresh || false
        };
        
        // 缓存结果
        pageStatusCache.set(tabId, { ...result, lastChecked: Date.now() });
        return result;
      } catch (error) {
        const result = {
          isValid: false,
          hasFormFields: false,
          needsRefresh: true,
          error: 'content_script_not_ready'
        };
        // 不缓存content script错误，因为可能很快就会好
        return result;
      }
    } catch (error) {
      Logger.error('Error checking page support:', error);
      return {
        isValid: false,
        hasFormFields: false,
        needsRefresh: true,
        error: 'unknown_error'
      };
    }
  }

  // 更新扩展图标的popup设置
  async function updatePopupBehavior(tabId: number, url?: string) {
    try {
      const pageStatus = await checkPageSupport(tabId, url);
      
      // 如果页面支持inpage popup，清除默认popup（这样点击图标会触发onClicked事件）
      if (pageStatus.isValid && pageStatus.hasFormFields && !pageStatus.needsRefresh) {
        await chrome.action.setPopup({ tabId, popup: '' });
        Logger.info(`Tab ${tabId}: Set to use inpage popup (cleared default popup)`);
      } else {
        // 如果页面不支持inpage popup，设置默认popup
        await chrome.action.setPopup({ tabId, popup: 'popup.html' });
        Logger.info(`Tab ${tabId}: Set to use Chrome popup (${pageStatus.error || 'no_form_fields'})`);
      }
    } catch (error) {
      Logger.error('Error updating popup behavior:', error);
      // 出错时默认使用Chrome popup
      try {
        await chrome.action.setPopup({ tabId, popup: 'popup.html' });
      } catch (fallbackError) {
        Logger.error('Error setting fallback popup:', fallbackError);
      }
    }
  }

  // 用于追踪最后一次用户信息更新时间
  let lastUserInfoFetch = 0
  const USER_INFO_MAX_AGE = 5 * 60 * 1000 // 用户信息最大缓存时间（5分钟）

  // Provider Models Configuration (reserved for future use)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const PROVIDER_MODELS = {
    openai: [
      { id: 'gpt-4', name: 'GPT-4' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
    ],
    claude: [
      { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
      { id: 'claude-2.1', name: 'Claude-2.1' }
    ],
    moonshot: [
      { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
      { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
      { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
    ],
    gemini: [
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash' },
      { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
    ],
    openrouter: [
      { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
      { id: 'openai/gpt-4', name: 'GPT-4' },
      { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo' },
      { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus' },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet' },
      { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B' },
      { id: 'mistralai/mistral-large', name: 'Mistral Large' }
    ],
    ollama: [
      { id: 'llama2', name: 'Llama 2' },
      { id: 'llama2:13b', name: 'Llama 2 13B' },
      { id: 'llama2:70b', name: 'Llama 2 70B' },
      { id: 'mistral', name: 'Mistral' },
      { id: 'codellama', name: 'Code Llama' },
      { id: 'phi', name: 'Phi' }
    ]
  } as const

  const defaultModels: DefaultModels = {
    openai: 'gpt-3.5-turbo',
    claude: 'claude-3-sonnet-20240229',
    moonshot: 'moonshot-v1-32k',
    gemini: 'gemini-2.0-flash',
    openrouter: 'openai/gpt-3.5-turbo',
    ollama: 'llama2'
  } as const;

  // 获取用户信息
  async function fetchUserInfo(userId: string, forceUpdate = false): Promise<UserInfo | null> {
    const now = Date.now()
    // 如果不是强制更新，且缓存未过期，直接返回
    if (!forceUpdate && now - lastUserInfoFetch < USER_INFO_MAX_AGE) {
      const { user_info } = await chrome.storage.local.get('user_info')
      if (user_info) {
        return user_info
      }
    }

    lastUserInfoFetch = now
    try {
      const response = await fetch(`${API_BASE_URL}/users/get-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const data = await response.json()
      if (data.success && data.user) {
        await chrome.storage.local.set({
          user_info: data.user,
          user_info_timestamp: now
        })
        return data.user
      }
      return null
    } catch (error) {
      Logger.error('Error fetching user info:', error instanceof Error ? error.message : String(error))
      return null
    }
  }

  // 设置跳过登录状态
  async function setSkipLogin(skip: boolean): Promise<void> {
    try {
      skipLogin = skip
      await chrome.storage.sync.set({ [STORAGE_KEYS.SKIP_LOGIN]: skip })
      Logger.info('Skip login status updated:', skip)
    } catch (error) {
      Logger.error('Error setting skip login status:', error)
    }
  }



  // 检查登录状态 - 优化版本
  async function checkLoginStatus(): Promise<boolean> {
    try {
      if (!chrome.cookies) {
        Logger.error('Cookies API not available')
        return false
      }

      // 先检查是否跳过登录
      const { formify_skip_login } = await chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN)
      skipLogin = !!formify_skip_login

      const cookie = await chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).catch(error => {
        Logger.error('Error getting cookie:', error)
        return null
      })

      const newLoginState = !!cookie?.value

      if (newLoginState !== isLoggedIn) {
        isLoggedIn = newLoginState
        
        // 安全地发送登录状态变化消息，避免 "Receiving end does not exist" 错误
        try {
          // 使用 chrome.runtime.sendMessage 的安全包装
          const tabs = await chrome.tabs.query({})
          const activeTab = tabs.find(tab => tab.active) || tabs[0]
          
          if (activeTab && activeTab.id) {
            // 尝试发送到 content script
            try {
              await chrome.tabs.sendMessage(activeTab.id, {
                type: 'loginStatusChanged',
                isLoggedIn,
                skipLogin,
                token: cookie?.value
              })
            } catch (contentError) {
              // content script 可能还没加载，这是正常的
              console.debug('[Background] Content script not ready:', contentError instanceof Error ? contentError.message : String(contentError))
            }
          }

          // 发送到 popup（如果打开的话）
          try {
            await chrome.runtime.sendMessage({
              type: 'loginStatusChanged',
              isLoggedIn,
              skipLogin,
              token: cookie?.value
            })
          } catch (popupError) {
            // popup 可能没有打开，这是正常的
            console.debug('[Background] Popup not open:', popupError instanceof Error ? popupError.message : String(popupError))
          }
        } catch (error) {
          // 只有在确实是严重错误时才记录
          const errorMessage = error instanceof Error ? error.message : String(error)
          if (!errorMessage.includes('Could not establish connection') &&
              !errorMessage.includes('Receiving end does not exist')) {
            Logger.error('Error sending login status message:', error)
          }
        }

        try {
          if (!isLoggedIn) {
            await chrome.storage.local.remove('user_info')
          } else if (cookie?.value) {
            await fetchUserInfo(cookie.value, true)
          }
        } catch (error) {
          Logger.error('Error updating user info:', error)
        }
      }

      return isLoggedIn
    } catch (error) {
      Logger.error('Error checking login status:', error)
      return false
    }
  }

  async function checkAndUpdateLoginStatus(): Promise<boolean> {
    try {
      const loginStatus = await checkLoginStatus()
      if (loginStatus) {
        const cookie = await chrome.cookies.get({
          url: COOKIE_URL,
          name: 'xToken'
        }).catch(() => null)

        if (cookie?.value) {
          await fetchUserInfo(cookie.value).catch(error => {
            Logger.error('Error fetching user info:', error)
          })
        }
      }
      return loginStatus
    } catch (error) {
      Logger.error('Error in checkAndUpdateLoginStatus:', error)
      return false
    }
  }

  // Handle AI request
  async function handleAiRequest(request: any, sendResponse: (response: any) => void = () => {}) {
    try {
      Logger.info('Handling AI request:', request)

      const storage = await chrome.storage.sync.get([
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.API_KEYS,
        STORAGE_KEYS.VALIDATED_KEYS,
        STORAGE_KEYS.PROJECTS
      ])

      const settings: Settings = storage[STORAGE_KEYS.SETTINGS] || {}
      const apiKeys: ApiKeys = storage[STORAGE_KEYS.API_KEYS] || {}
      const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
      const projects = storage[STORAGE_KEYS.PROJECTS] || []

      const provider = settings.defaultProvider || 'openai'

      // 检查 API Key 是否已验证
      if (!validatedKeys[provider]) {
        throw new Error(`No validated API key for ${provider}`)
      }

      const model = settings.defaultModel || defaultModels[provider as keyof DefaultModels]

      // 获取项目信息
      let projectInfo: { name: any; description: any; environment: any; template: any; } | undefined = undefined
      if (request.options.projectId) {
        Logger.info('Looking for project with ID:', request.options.projectId)
        Logger.info('Available projects:', projects)
        const project = projects.find((p: any) => p.id === request.options.projectId)
        if (project) {
          Logger.info('Found project:', project)
          projectInfo = {
            name: project.name,
            description: project.description,
            environment: project.environment,
            template: project.template
          }
          Logger.info('Project info created:', projectInfo)
        } else {
          Logger.error('Project not found with ID:', request.options.projectId)
        }
      } else {
        Logger.info('No project ID provided in request')
      }

      // 构建FormRequest对象
      const formRequest: FormRequest = {
        description: request.options.description,
        formFields: request.options.formFields,
        mode: request.options.mode,
        language: request.options.language,
        project: projectInfo,
        provider: provider,
        model: model,
        apiKey: apiKeys[provider],
        useCustomApi: settings.useCustomApi
      }

      // 生成prompt
      const prompt = generatePrompt(formRequest)
      Logger.info('Generated prompt:', prompt)

      // 创建AI provider并生成内容
      let aiProvider
      if (provider === 'ollama') {
        // For Ollama, pass the endpoint as apiKey and additional options
        const ollamaEndpoint = settings.ollama_endpoint || apiKeys[provider] || 'http://localhost:11434'
        aiProvider = createAIProvider(provider, ollamaEndpoint, model, { endpoint: ollamaEndpoint })
      } else {
        aiProvider = createAIProvider(provider, apiKeys[provider], model)
      }

      // 检查是否支持流式处理（检查所有 Ollama 模型和推理模型）
      const isReasoningModel = model.toLowerCase().includes('reason') ||
                              model.toLowerCase().includes('o1') ||
                              model.toLowerCase().includes('think');
      
      const supportsStreaming = typeof (aiProvider as any).generateContentStream === 'function';
      const isOllama = provider === 'ollama';

      if (supportsStreaming && (isReasoningModel || isOllama)) {
        // 使用流式处理（Ollama 或推理模型）
        Logger.info('Using streaming for model:', { model, provider, isReasoningModel, isOllama });
        return await handleStreamingAiRequest(aiProvider as any, prompt, request, sendResponse, model, provider);
      } else {
        // 使用非流式处理
        Logger.info('Using non-streaming for model:', { model, provider, isReasoningModel, isOllama, supportsStreaming });
        const aiResponse = await aiProvider.generateContent(prompt);
        Logger.info('AI response received:', aiResponse);
        return await handleNonStreamingAiRequest(aiResponse, model, provider, request);
      }

    } catch (error: any) {
      Logger.error('Error in AI request:', error)

      // 处理AI Provider错误
      if (error instanceof AIProviderError) {
        return {
          success: false,
          error: `API error: ${error.message}`
        }
      }

      return {
        success: false,
        error: error.message || 'Failed to process AI request'
      }
    }
  }

  // 验证 API Key (reserved for future use)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async function validateApiKey(provider: string, key: string) {
    try {
      let endpoint = '';
      let testPayload = {};

      switch (provider) {
        case 'openai':
          endpoint = 'https://api.openai.com/v1/chat/completions';
          testPayload = {
            model: "gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'claude':
          endpoint = 'https://api.anthropic.com/v1/messages';
          testPayload = {
            model: "claude-3-sonnet-20240229",
            max_tokens: 1,
            messages: [{ role: "user", content: "test" }]
          };
          break;

        case 'moonshot':
          endpoint = 'https://api.moonshot.cn/v1/chat/completions';
          testPayload = {
            model: "moonshot-v1-8k",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'gemini':
          endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
          testPayload = {
            contents: [{ parts: [{ text: "test" }] }]
          };
          break;

        case 'deepseek':
          endpoint = 'https://api.deepseek.com/v1/chat/completions';
          testPayload = {
            model: "deepseek-chat",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'openrouter':
          endpoint = 'https://openrouter.ai/api/v1/chat/completions';
          testPayload = {
            model: "openai/gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'ollama':
          // 对于 Ollama，我们测试连接到本地服务器
          const ollamaEndpoint = key || 'http://localhost:11434'; // key 作为端点地址
          endpoint = `${ollamaEndpoint}/v1/models`;
          // 对于 Ollama，我们只需要测试模型列表端点
          const modelsResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ollama',
              'Accept': 'application/json'
            }
          });

          if (!modelsResponse.ok) {
            if (modelsResponse.status === 0) {
              throw new Error(`Cannot connect to Ollama server at ${ollamaEndpoint}. Please check if Ollama is running and accessible.`);
            }
            throw new Error(`Ollama server responded with status ${modelsResponse.status}. Please check your Ollama installation.`);
          }

          return { success: true };

        default:
          throw new Error('Unsupported provider');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 根据不同提供商设置认证头
      switch (provider) {
        case 'openai':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'claude':
          headers['x-api-key'] = key;
          headers['anthropic-version'] = '2023-06-01';
          break;
        case 'moonshot':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'gemini':
          endpoint = `${endpoint}?key=${key}`;
          break;
        case 'deepseek':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'openrouter':
          headers['Authorization'] = `Bearer ${key}`;
          headers['HTTP-Referer'] = 'https://fillify.tech';
          headers['X-Title'] = 'Fillify';
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(testPayload)
      });

      const data = await response.json();

      // 检查响应是否包含错误
      if (response.status !== 200) {
        throw new Error(data.error?.message || 'Invalid API key');
      }

      return { success: true };
    } catch (error) {
      Logger.error('API key validation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate API key'
      };
    }
  }

  // Event Listeners
  chrome.runtime.onInstalled.addListener(async (details) => {
    if (details.reason === 'install') {
      Logger.info('Extension installed')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              useCustomApi: true,
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })

      await checkAndUpdateLoginStatus()
      chrome.tabs.create({
        url: chrome.runtime.getURL('onboarding.html')
      })
    } else if (details.reason === 'update') {
      Logger.info('Extension updated')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              useCustomApi: true,
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })
    }
  })

  // 添加 cookie 监听功能
  chrome.cookies.onChanged.addListener(async (changeInfo: CookieChangeInfo) => {
    const { cookie, removed, cause } = changeInfo
    Logger.info('Cookie changed:', { cookie, removed, cause })

    if (cookie.domain.includes(COOKIE_DOMAIN) && cookie.name === 'xToken') {
      Logger.info('xToken cookie changed:', { value: cookie.value, removed })
      // 当 cookie 变化时，立即检查登录状态并强制更新用户信息
      const status = await checkLoginStatus()
      if (status && !removed) {
        await fetchUserInfo(cookie.value, true)
      }
    }
  })

  // 注册消息监听器
  chrome.runtime.onMessage.addListener((
    request: Message,
    _sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) => {
    Logger.info('Received message:', request)

    if (request.type === 'getLoginStatus') {
      // 确保我们返回最新的登录状态
      checkLoginStatus().then(status => {
        sendResponse({ 
          isLoggedIn: status, 
          skipLogin,
          timestamp: Date.now() // 添加时间戳以确保数据新鲜度
        })
      }).catch(error => {
        Logger.error('Error checking login status for getLoginStatus:', error)
        sendResponse({ 
          isLoggedIn: false, 
          skipLogin,
          error: error.message,
          timestamp: Date.now()
        })
      })
      return true
    }

    if (request.type === 'setSkipLogin') {
      setSkipLogin(request.skip).then(() => {
        sendResponse({ success: true })
      }).catch(error => {
        Logger.error('Error setting skip login:', error)
        sendResponse({ success: false, error: String(error) })
      })
      return true
    }

    if (request.type === 'getUserInfo') {
      chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).then(async (cookie) => {
        if (cookie?.value) {
          const userInfo = await fetchUserInfo(cookie.value)
          sendResponse({ success: true, user: userInfo })
        } else {
          sendResponse({ success: false })
        }
      })
      return true
    }

    if (request.type === 'aiRequest') {
      handleAiRequest(request, sendResponse).then(response => {
        // 如果是流式响应，则 sendResponse 已经在 handleStreamingAiRequest 中被调用
        // 这里只处理非流式响应
        if (response && !response.streaming) {
          sendResponse(response)
        }
      }).catch(error => {
        sendResponse({ success: false, error: error.message })
      })
      return true
    }

    if (request.type === 'openLoginPage') {
      chrome.tabs.create({ url: 'https://fillify.tech/signin' })
      sendResponse({ success: true })
      return true
    }

    if (request.type === 'openDashboard') {
      chrome.tabs.create({ url: 'https://fillify.tech/dashboard' })
      sendResponse({ success: true })
      return true
    }
    if (request.type === 'openSettings') {
      chrome.tabs.create({
        url: chrome.runtime.getURL('settings.html')
      })
      sendResponse({ success: true })
      return true
    }
    if (request.type === 'openProjectSettings') {
      chrome.tabs.create({
        url: chrome.runtime.getURL('settings.html?tab=library&action=add_project')
      })
      sendResponse({ success: true })
      return true
    }
    if (request.type === 'signOut') {
      (async () => {
        try {
          // 执行登出逻辑
          await fetch('https://fillify-343190162770.asia-east1.run.app/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
          }).catch((error) => {
            Logger.error('Sign out API error:', error)
            // 即使服务器请求失败，也继续清除本地状态
          })

          // 清除cookie
          try {
            await chrome.cookies.remove({
              url: COOKIE_URL,
              name: 'xToken'
            })
          } catch (cookieError) {
            Logger.error('Error removing cookie:', cookieError)
          }

          // 清除本地状态
          isLoggedIn = false
          skipLogin = false

          // 清除本地存储的用户信息
          try {
            await chrome.storage.local.remove('user_info')
            await chrome.storage.sync.remove('formify_skip_login')
          } catch (storageError) {
            Logger.error('Error clearing storage:', storageError)
          }

          Logger.info('Sign out completed successfully')
          sendResponse({ success: true })
        } catch (error) {
          Logger.error('Sign out error:', error)
          // 即使出错也清除本地状态
          isLoggedIn = false
          skipLogin = false
          sendResponse({ success: true })
        }
      })()
      return true
    }

    if (request.action === 'getSettings') {
      chrome.storage.sync.get([STORAGE_KEYS.SETTINGS, STORAGE_KEYS.API_KEYS], (data) => {
        sendResponse({
          settings: data[STORAGE_KEYS.SETTINGS] || {},
          apiKeys: data[STORAGE_KEYS.API_KEYS] || {}
        })
      })
      return true
    }

    if (request.action === 'updateSettings') {
      chrome.storage.sync.set({
        formify_settings: request.settings
      }, () => {
        sendResponse({ success: true })
      })
      return true
    }
    
    if (request.type === 'contentScriptInitialized') {
      Logger.info('Content script initialized on:', request.url)
      
      // 使用sender.tab信息来获取tab ID并更新popup行为
      if (_sender.tab?.id) {
        // 清除缓存并重新检查页面状态
        pageStatusCache.delete(_sender.tab.id)
        updatePopupBehavior(_sender.tab.id, _sender.tab.url).then(() => {
          sendResponse({ success: true })
        }).catch(error => {
          Logger.error('Error updating popup behavior after content script init:', error)
          sendResponse({ success: true }) // 还是返回成功，不影响content script
        })
        return true // 表示异步响应
      } else {
        sendResponse({ success: true })
        return true
      }
    }

    if (request.type === 'openChromePopupForError') {
      Logger.info('Opening Chrome popup due to in-page popup error:', request.reason)

      // 存储错误信息，供popup使用
      chrome.storage.local.set({
        'popup_error_trigger': {
          reason: request.reason,
          pageStatus: request.pageStatus,
          error: request.error,
          timestamp: Date.now()
        }
      }).then(() => {
        // 打开Chrome popup
        chrome.action.openPopup().then(() => {
          sendResponse({ success: true })
        }).catch(error => {
          Logger.error('Failed to open Chrome popup:', error)
          sendResponse({ success: false, error: error.message })
        })
      }).catch(storageError => {
        Logger.error('Failed to store error trigger info:', storageError)
        sendResponse({ success: false, error: storageError.message })
      })

      return true
    }

    // 处理来自 InPagePopup 的 fillForm 消息
    if (request.type === 'fillForm') {
      // 转发 fillForm 消息到当前活动的 content script
      chrome.tabs.query({ active: true, currentWindow: true }).then(tabs => {
        if (tabs[0]?.id) {
          chrome.tabs.sendMessage(tabs[0].id, request).then(response => {
            sendResponse(response)
          }).catch(error => {
            Logger.error('Error forwarding fillForm to content script:', error)
            sendResponse({ success: false, error: error.message })
          })
        } else {
          sendResponse({ success: false, error: 'No active tab found' })
        }
      }).catch(error => {
        Logger.error('Error querying tabs for fillForm:', error)
        sendResponse({ success: false, error: error.message })
      })
      return true
    }

    // 处理来自 InPagePopup 的 showGeneratingEffect 消息
    if (request.type === 'showGeneratingEffect') {
      // 转发消息到当前活动的 content script
      chrome.tabs.query({ active: true, currentWindow: true }).then(tabs => {
        if (tabs[0]?.id) {
          chrome.tabs.sendMessage(tabs[0].id, request).then(response => {
            sendResponse(response || { success: true })
          }).catch(error => {
            Logger.error('Error forwarding showGeneratingEffect to content script:', error)
            sendResponse({ success: false, error: error.message })
          })
        } else {
          sendResponse({ success: false, error: 'No active tab found' })
        }
      }).catch(error => {
        Logger.error('Error querying tabs for showGeneratingEffect:', error)
        sendResponse({ success: false, error: error.message })
      })
      return true
    }

    // 处理来自 InPagePopup 的 removeGeneratingEffect 消息
    if (request.type === 'removeGeneratingEffect') {
      // 转发消息到当前活动的 content script
      chrome.tabs.query({ active: true, currentWindow: true }).then(tabs => {
        if (tabs[0]?.id) {
          chrome.tabs.sendMessage(tabs[0].id, request).then(response => {
            sendResponse(response || { success: true })
          }).catch(error => {
            Logger.error('Error forwarding removeGeneratingEffect to content script:', error)
            sendResponse({ success: false, error: error.message })
          })
        } else {
          sendResponse({ success: false, error: 'No active tab found' })
        }
      }).catch(error => {
        Logger.error('Error querying tabs for removeGeneratingEffect:', error)
        sendResponse({ success: false, error: error.message })
      })
      return true
    }

    // 默认处理未知消息类型
    Logger.error('Unknown message type in background:', request.type || 'undefined', 'Full message:', JSON.stringify(request))
    sendResponse({ success: false, error: `Unknown message type: ${request.type || 'undefined'}` })
    return true
  })

  // 设置右键菜单监听器
  setupContextMenuListener()

  // 延迟创建右键菜单，确保扩展完全初始化
  setTimeout(() => {
    createContextMenu()
  }, 1000)

  // 监听扩展图标点击事件（只在没有设置默认popup时才会触发）
  chrome.action.onClicked.addListener(async (tab) => {
    console.log('[Fillify] Extension icon clicked on tab:', tab.url)
    if (!tab.id) return;

    try {
      // 尝试切换inpage popup（toggle功能）
      const response = await chrome.tabs.sendMessage(tab.id, {
        type: 'toggleInPagePopup'
      });
      
      if (response?.success) {
        console.log('[Fillify] Inpage popup toggled successfully');
      } else {
        console.log('[Fillify] Failed to toggle inpage popup, updating popup behavior');
        // 如果无法切换inpage popup，可能页面状态发生了变化，重新检查
        pageStatusCache.delete(tab.id);
        await updatePopupBehavior(tab.id, tab.url);
      }
    } catch (error) {
      console.error('[Fillify] Error handling icon click:', error);
      // 出错时重新检查页面状态
      pageStatusCache.delete(tab.id);
      await updatePopupBehavior(tab.id, tab.url);
    }
  });

  // 监听tab激活事件
  chrome.tabs.onActivated.addListener(async (activeInfo) => {
    try {
      const tab = await chrome.tabs.get(activeInfo.tabId);
      await updatePopupBehavior(activeInfo.tabId, tab.url);
    } catch (error) {
      Logger.error('Error handling tab activation:', error);
    }
  });

  // 监听tab更新事件
  chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    // 只在页面加载完成或URL变化时更新
    if (changeInfo.status === 'complete' || changeInfo.url) {
      // 清除缓存，因为页面可能发生了变化
      pageStatusCache.delete(tabId);
      await updatePopupBehavior(tabId, tab.url);
    }
  });

  // 监听tab移除事件，清理缓存
  chrome.tabs.onRemoved.addListener((tabId) => {
    pageStatusCache.delete(tabId);
  });

  // 监听扩展安装/启动事件
  chrome.runtime.onInstalled.addListener(() => {
    console.log('[Fillify] Extension installed/updated, creating context menu')
    setTimeout(() => {
      createContextMenu()
    }, 500)
  })

  chrome.runtime.onStartup.addListener(() => {
    console.log('[Fillify] Extension startup, creating context menu')
    setTimeout(() => {
      createContextMenu()
    }, 500)
  })
})

// 检查页面是否支持inpage popup（全局函数）
async function checkPageSupport(tabId: number, url?: string): Promise<{
  isValid: boolean;
  hasFormFields: boolean;
  needsRefresh: boolean;
  error?: string;
}> {
  try {
    // 由于这是全局函数，我们不能访问内部的缓存，每次都重新检查
    // 但对于右键菜单的使用场景，这是可以接受的

    // 检查是否为特殊页面
    if (url && (
      url.startsWith('chrome://') ||
      url.startsWith('chrome-extension://') ||
      url.startsWith('edge://') ||
      url.startsWith('about:') ||
      url.startsWith('moz-extension://')
    )) {
      return {
        isValid: false,
        hasFormFields: false,
        needsRefresh: false,
        error: 'special_page'
      };
    }

    // 向content script查询页面状态
    try {
      const response = await chrome.tabs.sendMessage(tabId, { type: 'checkPageStatus' });
      return {
        isValid: response?.isValid || false,
        hasFormFields: response?.hasFormFields || false,
        needsRefresh: response?.needsRefresh || false
      };
    } catch (error) {
      return {
        isValid: false,
        hasFormFields: false,
        needsRefresh: true,
        error: 'content_script_not_ready'
      };
    }
  } catch (error) {
    console.error('Error checking page support:', error);
    return {
      isValid: false,
      hasFormFields: false,
      needsRefresh: true,
      error: 'unknown_error'
    };
  }
}

// 创建右键菜单
function createContextMenu() {
  // 检查是否在开发环境或contextMenus API是否可用
  if (typeof chrome.contextMenus === 'undefined') {
    console.log('[Fillify] ContextMenus API not available, skipping menu creation')
    return
  }

  try {
    // 先清除可能存在的旧菜单项
    chrome.contextMenus.removeAll(() => {
      // 创建新的菜单项
      chrome.contextMenus.create({
        id: 'fillify-open-popup',
        title: 'Open Fillify',
        contexts: ['page', 'selection', 'editable'],
        documentUrlPatterns: ['<all_urls>'] // 改为支持所有URL
      }, () => {
        if (chrome.runtime.lastError) {
          console.error('[Fillify] Error creating context menu:', chrome.runtime.lastError)
        } else {
          console.log('[Fillify] Context menu created successfully')
        }
      })
    })
  } catch (error) {
    console.error('[Fillify] Failed to create context menu:', error)
  }
}

// 设置右键菜单监听器
function setupContextMenuListener() {
  if (typeof chrome.contextMenus !== 'undefined' && chrome.contextMenus.onClicked) {
    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      console.log('[Fillify] Context menu clicked:', info.menuItemId)
      if (info.menuItemId === 'fillify-open-popup' && tab?.id) {
        console.log('[Fillify] Handling context menu click')
        
        // 检查当前页面是否支持inpage popup
        const pageStatus = await checkPageSupport(tab.id, tab.url);
        
        if (pageStatus.isValid && pageStatus.hasFormFields && !pageStatus.needsRefresh) {
          // 支持inpage popup，切换显示状态
          try {
            const response = await chrome.tabs.sendMessage(tab.id, {
              type: 'toggleInPagePopup'
            });
            
            if (response?.success) {
              console.log('[Fillify] Inpage popup toggled from context menu');
            } else {
              console.log('[Fillify] Failed to toggle inpage popup from context menu, showing Chrome popup');
              chrome.action.openPopup();
            }
          } catch (error) {
            console.error('[Fillify] Error toggling inpage popup from context menu:', error);
            chrome.action.openPopup();
          }
        } else {
          // 不支持inpage popup，显示Chrome popup
          console.log('[Fillify] Page does not support inpage popup, showing Chrome popup');
          chrome.action.openPopup();
        }
      }
    })
    console.log('[Fillify] Context menu listener setup complete')
  } else {
    console.log('[Fillify] ContextMenus API not available for listener setup')
  }
}
