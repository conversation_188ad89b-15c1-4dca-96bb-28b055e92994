{"permissions": {"allow": ["Bash(cd \"/Users/<USER>/Downloads/Github/fillify/wxt\")", "Bash(npm run build)", "Bash(npm run lint)", "Bash(npm run type-check)", "Bash(npm run dev)", "Bash(grep -n \"console.error\\|throw\\|Error\" /Users/<USER>/Downloads/Github/fillify/wxt/.output/chrome-mv3/content-scripts/content.js)", "Bash(find /Users/<USER>/Downloads/Github/fillify/wxt -name \"*inject*\" -type f)", "Bash(find node_modules -name \"*.d.ts\" -path \"*/wxt/*\")", "Bash(find node_modules -name \"*.d.ts\" -path \"*/wxt/client*\")", "Bash(find node_modules -name \"*client*\" -path \"*/wxt/*\")", "Bash(ls node_modules/wxt/dist/client/)", "Bash(ls node_modules/wxt/dist/client/content-scripts/)", "Bash(ls node_modules/wxt/dist/client/content-scripts/ui/)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/src/services/reasoning-models.ts)", "Bash(npm run typecheck)", "Bash(npm run)", "Bash(pkill -f \"wxt\")", "Bash(npm run compile)"], "deny": []}}